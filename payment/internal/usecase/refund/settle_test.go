package refund

import (
	"context"
	"testing"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/suite"
	"gitlab.zalopay.vn/fin/installment/payment-service/configs"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
	adapter_mocks "gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/mocks/adapters"
	repository_mocks "gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/usecase/mocks/repository"
	"go.uber.org/mock/gomock"
)

type SettleTestSuite struct {
	suite.Suite
	ctrl    *gomock.Controller
	usecase *Usecase

	// Repository mocks
	mockRefundRepo  *repository_mocks.MockRefundRepo
	mockPaymentRepo *repository_mocks.MockPaymentRepo
	mockOrderRepo   *repository_mocks.MockOrderRepo
	mockTransaction *repository_mocks.MockTransaction

	// Adapter mocks
	mockAccountAdapter       *adapter_mocks.MockAccountAdapter
	mockPartnerConnector     *adapter_mocks.MockPartnerConnector
	mockAcquiringCore        *adapter_mocks.MockAcquiringCoreAdapter
	mockInstallmentAdapter   *adapter_mocks.MockInstallmentAdapter
	mockTaskJobAdapter       *adapter_mocks.MockTaskJobAdapter
	mockDistributedLock      *adapter_mocks.MockDistributedLock
	mockRefundSettleNotifier *adapter_mocks.MockRefundSettleNotifier

	// Test configs
	paymentConfig      *configs.Payment
	orderConfigsHelper *configs.OrderConfigsHelper
	logger             log.Logger
}

func (suite *SettleTestSuite) SetupTest() {
	suite.ctrl = gomock.NewController(suite.T())

	// Initialize repository mocks
	suite.mockRefundRepo = repository_mocks.NewMockRefundRepo(suite.ctrl)
	suite.mockPaymentRepo = repository_mocks.NewMockPaymentRepo(suite.ctrl)
	suite.mockOrderRepo = repository_mocks.NewMockOrderRepo(suite.ctrl)
	suite.mockTransaction = repository_mocks.NewMockTransaction(suite.ctrl)

	// Initialize adapter mocks
	suite.mockAccountAdapter = adapter_mocks.NewMockAccountAdapter(suite.ctrl)
	suite.mockPartnerConnector = adapter_mocks.NewMockPartnerConnector(suite.ctrl)
	suite.mockAcquiringCore = adapter_mocks.NewMockAcquiringCoreAdapter(suite.ctrl)
	suite.mockInstallmentAdapter = adapter_mocks.NewMockInstallmentAdapter(suite.ctrl)
	suite.mockTaskJobAdapter = adapter_mocks.NewMockTaskJobAdapter(suite.ctrl)
	suite.mockDistributedLock = adapter_mocks.NewMockDistributedLock(suite.ctrl)
	suite.mockRefundSettleNotifier = adapter_mocks.NewMockRefundSettleNotifier(suite.ctrl)

	// Initialize configs
	suite.logger = log.DefaultLogger
	suite.paymentConfig = &configs.Payment{
		Refund: &configs.Refund{},
	}
	suite.orderConfigsHelper = &configs.OrderConfigsHelper{}

	// Initialize usecase with all dependencies
	suite.usecase = NewRefundUsecase(
		suite.logger,
		suite.mockRefundRepo,
		suite.mockTransaction,
		suite.mockTaskJobAdapter,
		suite.mockDistributedLock,
		suite.paymentConfig,
		suite.orderConfigsHelper,
		suite.mockOrderRepo,
		suite.mockPaymentRepo,
		suite.mockPartnerConnector,
		suite.mockAcquiringCore,
		suite.mockAccountAdapter,
		suite.mockRefundSettleNotifier,
		suite.mockInstallmentAdapter,
	)
}

func (suite *SettleTestSuite) TearDownTest() {
	suite.ctrl.Finish()
}

func (suite *SettleTestSuite) TestProcessRefundSettleInfo() {
	ctx := context.Background()
	settleID := int64(123)

	tests := []struct {
		name          string
		setupMocks    func()
		expectedError string
	}{
		{
			name: "success_process_settle_info",
			setupMocks: func() {
				refundSettle := &model.RefundSettle{
					ID:     settleID,
					Status: model.RefundSettleStatusInit,
				}

				suite.mockTransaction.EXPECT().
					BeginTx(ctx).
					Return(ctx, nil)

				suite.mockRefundRepo.EXPECT().
					GetRefundSettleForUpdate(ctx, settleID).
					Return(refundSettle, nil)

				suite.mockRefundRepo.EXPECT().
					UpdateRefundSettleStatusByID(ctx, settleID, model.RefundSettleStatusPending).
					Return(nil)

				suite.mockTransaction.EXPECT().
					CommitTx(ctx).
					Return(nil)

				suite.mockRefundSettleNotifier.EXPECT().
					PublishRefundSettleEvent(ctx, refundSettle).
					Return(nil)

				suite.mockTransaction.EXPECT().
					RollbackTx(ctx).
					Return(nil)
			},
			expectedError: "",
		},
		{
			name: "error_begin_tx",
			setupMocks: func() {
				suite.mockTransaction.EXPECT().
					BeginTx(ctx).
					Return(nil, errors.New("begin tx error"))
			},
			expectedError: "begin tx error",
		},
		{
			name: "error_get_refund_settle",
			setupMocks: func() {
				suite.mockTransaction.EXPECT().
					BeginTx(ctx).
					Return(ctx, nil)

				suite.mockRefundRepo.EXPECT().
					GetRefundSettleForUpdate(ctx, settleID).
					Return(nil, errors.New("get settle error"))

				suite.mockTransaction.EXPECT().
					RollbackTx(ctx).
					Return(nil)
			},
			expectedError: "get settle error",
		},
		{
			name: "no_trigger_settlement_condition",
			setupMocks: func() {
				refundSettle := &model.RefundSettle{
					ID:     settleID,
					Status: model.RefundSettleStatusCompleted,
				}

				suite.mockTransaction.EXPECT().
					BeginTx(ctx).
					Return(ctx, nil)

				suite.mockRefundRepo.EXPECT().
					GetRefundSettleForUpdate(ctx, settleID).
					Return(refundSettle, nil)

				suite.mockTransaction.EXPECT().
					RollbackTx(ctx).
					Return(nil)
			},
			expectedError: "",
		},
		{
			name: "error_update_status",
			setupMocks: func() {
				refundSettle := &model.RefundSettle{
					ID:     settleID,
					Status: model.RefundSettleStatusInit,
				}

				suite.mockTransaction.EXPECT().
					BeginTx(ctx).
					Return(ctx, nil)

				suite.mockRefundRepo.EXPECT().
					GetRefundSettleForUpdate(ctx, settleID).
					Return(refundSettle, nil)

				suite.mockRefundRepo.EXPECT().
					UpdateRefundSettleStatusByID(ctx, settleID, model.RefundSettleStatusPending).
					Return(errors.New("update status error"))

				suite.mockTransaction.EXPECT().
					RollbackTx(ctx).
					Return(nil)
			},
			expectedError: "update status error",
		},
		{
			name: "error_commit_tx",
			setupMocks: func() {
				refundSettle := &model.RefundSettle{
					ID:     settleID,
					Status: model.RefundSettleStatusInit,
				}

				suite.mockTransaction.EXPECT().
					BeginTx(ctx).
					Return(ctx, nil)

				suite.mockRefundRepo.EXPECT().
					GetRefundSettleForUpdate(ctx, settleID).
					Return(refundSettle, nil)

				suite.mockRefundRepo.EXPECT().
					UpdateRefundSettleStatusByID(ctx, settleID, model.RefundSettleStatusPending).
					Return(nil)

				suite.mockTransaction.EXPECT().
					CommitTx(ctx).
					Return(errors.New("commit error"))

				suite.mockTransaction.EXPECT().
					RollbackTx(ctx).
					Return(nil)
			},
			expectedError: "commit error",
		},
		{
			name: "error_publish_settle_request",
			setupMocks: func() {
				refundSettle := &model.RefundSettle{
					ID:     settleID,
					Status: model.RefundSettleStatusInit,
				}

				suite.mockTransaction.EXPECT().
					BeginTx(ctx).
					Return(ctx, nil)

				suite.mockRefundRepo.EXPECT().
					GetRefundSettleForUpdate(ctx, settleID).
					Return(refundSettle, nil)

				suite.mockRefundRepo.EXPECT().
					UpdateRefundSettleStatusByID(ctx, settleID, model.RefundSettleStatusPending).
					Return(nil)

				suite.mockTransaction.EXPECT().
					CommitTx(ctx).
					Return(nil)

				suite.mockRefundSettleNotifier.EXPECT().
					PublishRefundSettleEvent(ctx, refundSettle).
					Return(errors.New("publish error"))

				suite.mockTransaction.EXPECT().
					RollbackTx(ctx).
					Return(nil)
			},
			expectedError: "publish error",
		},
	}

	for _, tt := range tests {
		suite.Run(tt.name, func() {
			tt.setupMocks()

			err := suite.usecase.ProcessRefundSettleInfo(ctx, settleID)

			if tt.expectedError != "" {
				suite.Error(err)
				suite.Contains(err.Error(), tt.expectedError)
			} else {
				suite.NoError(err)
			}
		})
	}
}

func (suite *SettleTestSuite) TestPublishRefundSettleRequest() {
	ctx := context.Background()
	settle := &model.RefundSettle{
		ID:        123,
		ZPTransID: 456,
		Status:    model.RefundSettleStatusPending,
	}

	tests := []struct {
		name          string
		setupMocks    func()
		expectedError string
	}{
		{
			name: "success_publish_request",
			setupMocks: func() {
				suite.mockRefundSettleNotifier.EXPECT().
					PublishRefundSettleEvent(ctx, settle).
					Return(nil)
			},
			expectedError: "",
		},
		{
			name: "error_publish_event",
			setupMocks: func() {
				suite.mockRefundSettleNotifier.EXPECT().
					PublishRefundSettleEvent(ctx, settle).
					Return(errors.New("publish error"))
			},
			expectedError: "publish error",
		},
	}

	for _, tt := range tests {
		suite.Run(tt.name, func() {
			tt.setupMocks()

			err := suite.usecase.PublishRefundSettleRequest(ctx, settle)

			if tt.expectedError != "" {
				suite.Error(err)
				suite.Contains(err.Error(), tt.expectedError)
			} else {
				suite.NoError(err)
			}
		})
	}
}

func (suite *SettleTestSuite) TestGetRefundSettleInfo() {
	ctx := context.Background()
	settleID := int64(123)

	tests := []struct {
		name           string
		setupMocks     func()
		expectedError  string
		expectedSettle *model.RefundSettle
	}{
		{
			name: "success_get_settle_info",
			setupMocks: func() {
				settle := &model.RefundSettle{
					ID:        settleID,
					ZPTransID: 456,
					Status:    model.RefundSettleStatusPending,
				}
				suite.mockRefundRepo.EXPECT().
					GetRefundSettleByID(ctx, settleID).
					Return(settle, nil)
			},
			expectedError: "",
			expectedSettle: &model.RefundSettle{
				ID:        settleID,
				ZPTransID: 456,
				Status:    model.RefundSettleStatusPending,
			},
		},
		{
			name: "error_get_settle",
			setupMocks: func() {
				suite.mockRefundRepo.EXPECT().
					GetRefundSettleByID(ctx, settleID).
					Return(nil, errors.New("get settle error"))
			},
			expectedError:  "get settle error",
			expectedSettle: nil,
		},
	}

	for _, tt := range tests {
		suite.Run(tt.name, func() {
			tt.setupMocks()

			result, err := suite.usecase.GetRefundSettleInfo(ctx, settleID)

			if tt.expectedError != "" {
				suite.Error(err)
				suite.Contains(err.Error(), tt.expectedError)
				suite.Nil(result)
			} else {
				suite.NoError(err)
				suite.Equal(tt.expectedSettle, result)
			}
		})
	}
}

func (suite *SettleTestSuite) TestGetRefundSettleByZPTransID() {
	ctx := context.Background()
	zpTransID := int64(456)

	tests := []struct {
		name           string
		setupMocks     func()
		expectedError  string
		expectedSettle *model.RefundSettle
	}{
		{
			name: "success_get_settle_by_zp_trans_id",
			setupMocks: func() {
				settle := &model.RefundSettle{
					ID:        123,
					ZPTransID: zpTransID,
					Status:    model.RefundSettleStatusPending,
				}
				suite.mockRefundRepo.EXPECT().
					GetRefundSettleByZPTransID(ctx, zpTransID).
					Return(settle, nil)
			},
			expectedError: "",
			expectedSettle: &model.RefundSettle{
				ID:        123,
				ZPTransID: zpTransID,
				Status:    model.RefundSettleStatusPending,
			},
		},
		{
			name: "error_get_settle_by_zp_trans_id",
			setupMocks: func() {
				suite.mockRefundRepo.EXPECT().
					GetRefundSettleByZPTransID(ctx, zpTransID).
					Return(nil, errors.New("get settle error"))
			},
			expectedError:  "get settle error",
			expectedSettle: nil,
		},
	}

	for _, tt := range tests {
		suite.Run(tt.name, func() {
			tt.setupMocks()

			result, err := suite.usecase.GetRefundSettleByZPTransID(ctx, zpTransID)

			if tt.expectedError != "" {
				suite.Error(err)
				suite.Contains(err.Error(), tt.expectedError)
				suite.Nil(result)
			} else {
				suite.NoError(err)
				suite.Equal(tt.expectedSettle, result)
			}
		})
	}
}

func (suite *SettleTestSuite) TestSyncLatestSettlementAmount() {
	ctx := context.Background()
	settle := &model.RefundSettle{
		ID:               123,
		ZPTransID:        456,
		SettlementAmount: 1000,
	}

	tests := []struct {
		name          string
		setupMocks    func()
		expectedError string
	}{
		{
			name: "success_sync_amount_no_change",
			setupMocks: func() {
				earlyDischarge := model.EarlyDischarge{
					IsSettled:   true,
					IsEligible:  false,
					TotalAmount: 1000,
				}
				suite.mockInstallmentAdapter.EXPECT().
					GetEarlyDischarge(ctx, settle.ZPTransID, true).
					Return(earlyDischarge, nil)
			},
			expectedError: "",
		},
		{
			name: "success_sync_amount_with_change",
			setupMocks: func() {
				earlyDischarge := model.EarlyDischarge{
					IsSettled:   false,
					IsEligible:  true,
					TotalAmount: 1500,
				}
				suite.mockInstallmentAdapter.EXPECT().
					GetEarlyDischarge(ctx, settle.ZPTransID, true).
					Return(earlyDischarge, nil)

				updatedSettle := &model.RefundSettle{
					ID:               123,
					ZPTransID:        456,
					SettlementAmount: 1000,
				}
				suite.mockRefundRepo.EXPECT().
					GetRefundSettleByID(ctx, settle.ID).
					Return(updatedSettle, nil)

				suite.mockRefundRepo.EXPECT().
					UpdateRefundSettlementAmount(ctx, settle.ID, int64(1500)).
					Return(nil)
			},
			expectedError: "",
		},
		{
			name: "error_get_early_discharge",
			setupMocks: func() {
				suite.mockInstallmentAdapter.EXPECT().
					GetEarlyDischarge(ctx, settle.ZPTransID, true).
					Return(model.EarlyDischarge{}, errors.New("get early discharge error"))
			},
			expectedError: "get early discharge error",
		},
		{
			name: "error_validate_early_discharge",
			setupMocks: func() {
				earlyDischarge := model.EarlyDischarge{
					IsSettled:   false,
					IsEligible:  false,
					TotalAmount: 1500,
				}
				suite.mockInstallmentAdapter.EXPECT().
					GetEarlyDischarge(ctx, settle.ZPTransID, true).
					Return(earlyDischarge, nil)
			},
			expectedError: "invalid status",
		},
	}

	for _, tt := range tests {
		suite.Run(tt.name, func() {
			tt.setupMocks()

			result, err := suite.usecase.SyncLatestSettlementAmount(ctx, settle)

			if tt.expectedError != "" {
				suite.Error(err)
				suite.Contains(err.Error(), tt.expectedError)
			} else {
				suite.NoError(err)
				suite.NotNil(result)
			}
		})
	}
}

func (suite *SettleTestSuite) TestPublishRefundSettleResult() {
	ctx := context.Background()
	eadLog := &model.EarlyDischargeLog{
		ID: 123,
		Order: model.Order{
			AppTransID: "test_app_trans_id",
			AppID:      456,
		},
	}

	tests := []struct {
		name          string
		setupMocks    func()
		expectedError string
	}{
		{
			name: "success_publish_result",
			setupMocks: func() {
				order := &model.RefundSettleOrder{
					ID:             1,
					RefundSettleID: 789,
				}
				suite.mockOrderRepo.EXPECT().
					GetRefundSettleOrder(ctx, eadLog.Order.AppTransID, eadLog.Order.AppID).
					Return(order, nil)

				settle := &model.RefundSettle{
					ID:        789,
					ZPTransID: 456,
				}
				suite.mockRefundRepo.EXPECT().
					GetRefundSettleByID(ctx, order.RefundSettleID).
					Return(settle, nil)

				suite.mockRefundSettleNotifier.EXPECT().
					PublishRefundSettleResult(ctx, settle, eadLog).
					Return(nil)
			},
			expectedError: "",
		},
		{
			name: "error_get_refund_settle_order",
			setupMocks: func() {
				suite.mockOrderRepo.EXPECT().
					GetRefundSettleOrder(ctx, eadLog.Order.AppTransID, eadLog.Order.AppID).
					Return(nil, errors.New("get order error"))
			},
			expectedError: "get order error",
		},
		{
			name: "error_get_refund_settle",
			setupMocks: func() {
				order := &model.RefundSettleOrder{
					ID:             1,
					RefundSettleID: 789,
				}
				suite.mockOrderRepo.EXPECT().
					GetRefundSettleOrder(ctx, eadLog.Order.AppTransID, eadLog.Order.AppID).
					Return(order, nil)

				suite.mockRefundRepo.EXPECT().
					GetRefundSettleByID(ctx, order.RefundSettleID).
					Return(nil, errors.New("get settle error"))
			},
			expectedError: "get settle error",
		},
		{
			name: "error_publish_result",
			setupMocks: func() {
				order := &model.RefundSettleOrder{
					ID:             1,
					RefundSettleID: 789,
				}
				suite.mockOrderRepo.EXPECT().
					GetRefundSettleOrder(ctx, eadLog.Order.AppTransID, eadLog.Order.AppID).
					Return(order, nil)

				settle := &model.RefundSettle{
					ID:        789,
					ZPTransID: 456,
				}
				suite.mockRefundRepo.EXPECT().
					GetRefundSettleByID(ctx, order.RefundSettleID).
					Return(settle, nil)

				suite.mockRefundSettleNotifier.EXPECT().
					PublishRefundSettleResult(ctx, settle, eadLog).
					Return(errors.New("publish error"))
			},
			expectedError: "publish error",
		},
	}

	for _, tt := range tests {
		suite.Run(tt.name, func() {
			tt.setupMocks()

			err := suite.usecase.PublishRefundSettleResult(ctx, eadLog)

			if tt.expectedError != "" {
				suite.Error(err)
				suite.Contains(err.Error(), tt.expectedError)
			} else {
				suite.NoError(err)
			}
		})
	}
}

func (suite *SettleTestSuite) TestExecuteRefundSettlement() {
	ctx := context.Background()
	settleID := int64(123)

	tests := []struct {
		name          string
		setupMocks    func()
		expectedError string
	}{
		{
			name: "success_execute_settlement",
			setupMocks: func() {
				settle := &model.RefundSettle{
					ID:               settleID,
					ZPTransID:        456,
					Status:           model.RefundSettleStatusPending,
					SettlementAmount: 1000,
				}

				// First call to get settle info
				suite.mockRefundRepo.EXPECT().
					GetRefundSettleByID(ctx, settleID).
					Return(settle, nil)

				// SyncLatestSettlementAmount calls
				earlyDischarge := model.EarlyDischarge{
					IsSettled:   true,
					IsEligible:  false,
					TotalAmount: 1000,
				}
				suite.mockInstallmentAdapter.EXPECT().
					GetEarlyDischarge(ctx, settle.ZPTransID, true).
					Return(earlyDischarge, nil)

				// Since settlement amount matches, no update needed
				// HasSufficientFunds will return false, triggering reconcile job
				suite.mockTaskJobAdapter.EXPECT().
					ExecuteReconcileRefundSettleJob(ctx, &model.RefundSettleReconParams{ZPTransID: settle.ZPTransID}).
					Return(nil)
			},
			expectedError: "insufficient funds for settlement",
		},
		{
			name: "error_begin_tx",
			setupMocks: func() {
				settle := &model.RefundSettle{
					ID:               settleID,
					ZPTransID:        456,
					Status:           model.RefundSettleStatusPending,
					SettlementAmount: 1000,
					NetRefundAmount:  1000, // This makes HasSufficientFunds return true
				}

				// First call to get settle info
				suite.mockRefundRepo.EXPECT().
					GetRefundSettleByID(ctx, settleID).
					Return(settle, nil)

				// SyncLatestSettlementAmount calls
				earlyDischarge := model.EarlyDischarge{
					IsSettled:   true,
					IsEligible:  false,
					TotalAmount: 1000,
				}
				suite.mockInstallmentAdapter.EXPECT().
					GetEarlyDischarge(ctx, settle.ZPTransID, true).
					Return(earlyDischarge, nil)

				// Mock the resource gathering calls
				payment := &model.PurchaseOrder{
					ZpTransID: "456",
					AccountInfo: model.Account{
						ZalopayID:   12345,
						PartnerCode: "PARTNER_A",
					},
				}
				suite.mockPaymentRepo.EXPECT().
					GetPaymentByTransID(ctx, settle.ZPTransID).
					Return(payment, nil)

				account := model.Account{
					ZalopayID:   12345,
					PartnerCode: "PARTNER_A",
				}
				suite.mockAccountAdapter.EXPECT().
					GetAccount(ctx, payment.AccountInfo.ZalopayID, payment.AccountInfo.PartnerCode).
					Return(account, nil)

				bankRoute := &model.BankRoute{
					BankAccountNumber: "*********",
					BankAccountName:   "Test Account",
				}
				suite.mockPaymentRepo.EXPECT().
					GetBankRoute(ctx, account.PartnerCode, model.TransTypeRePayment).
					Return(bankRoute, nil)

				// This should fail
				suite.mockTransaction.EXPECT().
					BeginTx(ctx).
					Return(nil, errors.New("begin tx error"))
			},
			expectedError: "begin tx error",
		},
		{
			name: "error_update_status_processing",
			setupMocks: func() {
				settle := &model.RefundSettle{
					ID:               settleID,
					ZPTransID:        456,
					Status:           model.RefundSettleStatusPending,
					SettlementAmount: 1000,
					NetRefundAmount:  1000, // This makes HasSufficientFunds return true
				}

				// First call to get settle info
				suite.mockRefundRepo.EXPECT().
					GetRefundSettleByID(ctx, settleID).
					Return(settle, nil)

				// SyncLatestSettlementAmount calls
				earlyDischarge := model.EarlyDischarge{
					IsSettled:   true,
					IsEligible:  false,
					TotalAmount: 1000,
				}
				suite.mockInstallmentAdapter.EXPECT().
					GetEarlyDischarge(ctx, settle.ZPTransID, true).
					Return(earlyDischarge, nil)

				// Mock the resource gathering calls
				payment := &model.PurchaseOrder{
					ZpTransID: "456",
					AccountInfo: model.Account{
						ZalopayID:   12345,
						PartnerCode: "PARTNER_A",
					},
				}
				suite.mockPaymentRepo.EXPECT().
					GetPaymentByTransID(ctx, settle.ZPTransID).
					Return(payment, nil)

				account := model.Account{
					ZalopayID:   12345,
					PartnerCode: "PARTNER_A",
				}
				suite.mockAccountAdapter.EXPECT().
					GetAccount(ctx, payment.AccountInfo.ZalopayID, payment.AccountInfo.PartnerCode).
					Return(account, nil)

				bankRoute := &model.BankRoute{
					BankAccountNumber: "*********",
					BankAccountName:   "Test Account",
				}
				suite.mockPaymentRepo.EXPECT().
					GetBankRoute(ctx, account.PartnerCode, model.TransTypeRePayment).
					Return(bankRoute, nil)

				suite.mockTransaction.EXPECT().
					BeginTx(ctx).
					Return(ctx, nil)

				// This should fail
				suite.mockRefundRepo.EXPECT().
					UpdateRefundSettleStatusByID(ctx, settleID, model.RefundSettleStatusProcessing).
					Return(errors.New("update status error"))

				suite.mockTransaction.EXPECT().
					RollbackTx(ctx).
					Return(nil)
			},
			expectedError: "update status error",
		},
	}

	for _, tt := range tests {
		suite.Run(tt.name, func() {
			tt.setupMocks()

			err := suite.usecase.ExecuteRefundSettlement(ctx, settleID)

			if tt.expectedError != "" {
				suite.Error(err)
				suite.Contains(err.Error(), tt.expectedError)
			} else {
				suite.NoError(err)
			}
		})
	}
}

func (suite *SettleTestSuite) TestProcessRefundSettleResult() {
	ctx := context.Background()
	event := &RefundSettleResult{
		AppTransID:          "test_app_trans_id",
		AppID:               456,
		SettlePaymentStatus: model.PaymentStatusSucceeded,
		OrderID:             1,
		RefTransID:          789,
		SettlePaymentID:     100,
	}

	tests := []struct {
		name          string
		setupMocks    func()
		expectedError string
	}{
		{
			name: "success_process_result_succeeded",
			setupMocks: func() {
				order := &model.RefundSettleOrder{
					ID:             1,
					RefundSettleID: 789,
					Status:         model.OrderStatusSucceeded,
					Amount:         1000,
					AppTransID:     event.AppTransID,
					ZalopayID:      12345,
					Description:    "Test refund",
					ExtraData:      model.RefundOrderExtra{RefZPTransID: 456},
				}
				suite.mockOrderRepo.EXPECT().
					GetRefundSettleOrder(ctx, event.AppTransID, event.AppID).
					Return(order, nil)

				acOrderResp := &model.CreateRefundOrderResponse{
					AppID:        event.AppID,
					Amount:       order.Amount,
					AppTransID:   "new_app_trans_id",
					ZpTransToken: "token123",
					OrderNo:      12345,
					Description:  "test order",
					DataChecksum: "checksum123",
				}
				suite.mockAcquiringCore.EXPECT().
					CreateRefundSettleOrder(ctx, gomock.Any()).
					Return(acOrderResp, nil)

				suite.mockOrderRepo.EXPECT().
					UpdateOrderProgress(ctx, gomock.Any()).
					Return(nil)

				suite.mockAcquiringCore.EXPECT().
					SubmitRefundSettleOrder(ctx, gomock.Any()).
					Return(nil)

				suite.mockTransaction.EXPECT().
					WithTx(ctx, gomock.Any()).
					DoAndReturn(func(ctx context.Context, fn func(context.Context) error) error {
						return fn(ctx)
					})
			},
			expectedError: "",
		},
		{
			name: "success_process_result_failed",
			setupMocks: func() {
				order := &model.RefundSettleOrder{
					ID:             1,
					RefundSettleID: 789,
					Status:         model.OrderStatusPending,
				}
				suite.mockOrderRepo.EXPECT().
					GetRefundSettleOrder(ctx, "test_app_trans_id", int32(456)).
					Return(order, nil)

				// For failed status, only UpdateOrderProgress and UpdateRefundSettleStatusByID are called
				suite.mockOrderRepo.EXPECT().
					UpdateOrderProgress(ctx, gomock.Any()).
					Return(nil)

				suite.mockRefundRepo.EXPECT().
					UpdateRefundSettleStatusByID(ctx, order.RefundSettleID, model.RefundSettleStatusPending).
					Return(nil)

				suite.mockTransaction.EXPECT().
					WithTx(ctx, gomock.Any()).
					DoAndReturn(func(ctx context.Context, fn func(context.Context) error) error {
						return fn(ctx)
					})
			},
			expectedError: "",
		},
		{
			name: "error_get_refund_settle_order",
			setupMocks: func() {
				suite.mockOrderRepo.EXPECT().
					GetRefundSettleOrder(ctx, event.AppTransID, event.AppID).
					Return(nil, errors.New("get order error"))
			},
			expectedError: "get order error",
		},
		{
			name: "error_create_refund_settle_order",
			setupMocks: func() {
				order := &model.RefundSettleOrder{
					ID:             1,
					RefundSettleID: 789,
					Amount:         1000,
					AppTransID:     "test_app_trans_id",
					ZalopayID:      12345,
					Description:    "Test refund",
					ExtraData:      model.RefundOrderExtra{RefZPTransID: 456},
				}
				suite.mockOrderRepo.EXPECT().
					GetRefundSettleOrder(ctx, event.AppTransID, event.AppID).
					Return(order, nil)

				suite.mockAcquiringCore.EXPECT().
					CreateRefundSettleOrder(ctx, gomock.Any()).
					Return(nil, errors.New("create order error"))

				suite.mockTransaction.EXPECT().
					WithTx(ctx, gomock.Any()).
					DoAndReturn(func(ctx context.Context, fn func(context.Context) error) error {
						return fn(ctx)
					})
			},
			expectedError: "create order error",
		},
	}

	for _, tt := range tests {
		suite.Run(tt.name, func() {
			tt.setupMocks()

			// Use different event for failed case
			testEvent := event
			if tt.name == "success_process_result_failed" {
				testEvent = &RefundSettleResult{
					AppTransID:          "test_app_trans_id",
					AppID:               456,
					SettlePaymentStatus: model.PaymentStatusFailed,
					OrderID:             1,
					RefTransID:          789,
					SettlePaymentID:     100,
				}
			}

			err := suite.usecase.ProcessRefundSettleResult(ctx, testEvent)

			if tt.expectedError != "" {
				suite.Error(err)
				suite.Contains(err.Error(), tt.expectedError)
			} else {
				suite.NoError(err)
			}
		})
	}
}

func (suite *SettleTestSuite) TestProcessSettleOrderUpdate() {
	ctx := context.Background()
	orderUpdate := &SettleOrderUpdateRequest{
		AppTransID:   "test_app_trans_id",
		AppID:        456,
		OrderStatus:  model.OrderStatusSucceeded,
		ZPTransID:    789,
		OriginStatus: 1,
		ReasonStatus: "success",
	}

	tests := []struct {
		name          string
		setupMocks    func()
		expectedError string
	}{
		{
			name: "success_process_order_update",
			setupMocks: func() {
				order := &model.RefundSettleOrder{
					ID:             1,
					RefundSettleID: 789,
					Status:         model.OrderStatusPending,
					Type:           model.OrderTypeRefund,
				}
				suite.mockOrderRepo.EXPECT().
					GetRefundSettleOrder(ctx, orderUpdate.AppTransID, orderUpdate.AppID).
					Return(order, nil)

				suite.mockOrderRepo.EXPECT().
					UpdateOrderProgress(ctx, gomock.Any()).
					Return(nil)

				eadLog := &model.EarlyDischargeLog{
					ID: 100,
					Order: model.Order{
						AppTransID: orderUpdate.AppTransID,
						AppID:      orderUpdate.AppID,
					},
				}
				suite.mockPaymentRepo.EXPECT().
					GetEarlyDischargeLogByOrderID(ctx, order.ID).
					Return(eadLog, nil)

				suite.mockPaymentRepo.EXPECT().
					UpdatePaymentLogOrderIDs(ctx, eadLog.ID, gomock.Any()).
					Return(nil)

				settle := &model.RefundSettle{
					ID:        789,
					ZPTransID: 456,
					Status:    model.RefundSettleStatusProcessing,
				}
				suite.mockRefundRepo.EXPECT().
					GetRefundSettleForUpdate(ctx, order.RefundSettleID).
					Return(settle, nil)

				suite.mockRefundRepo.EXPECT().
					UpdateRefundSettleStatusByID(ctx, settle.ID, model.RefundSettleStatusSettled).
					Return(nil)

				suite.mockTransaction.EXPECT().
					WithTx(ctx, gomock.Any()).
					DoAndReturn(func(ctx context.Context, fn func(context.Context) error) error {
						return fn(ctx)
					})
			},
			expectedError: "",
		},
		{
			name: "error_get_refund_settle_order",
			setupMocks: func() {
				suite.mockOrderRepo.EXPECT().
					GetRefundSettleOrder(ctx, orderUpdate.AppTransID, orderUpdate.AppID).
					Return(nil, errors.New("get order error"))
			},
			expectedError: "get order error",
		},
		{
			name: "error_update_order_status",
			setupMocks: func() {
				order := &model.RefundSettleOrder{
					ID:             1,
					RefundSettleID: 789,
					Status:         model.OrderStatusPending,
					Type:           model.OrderTypeRefund,
				}
				suite.mockOrderRepo.EXPECT().
					GetRefundSettleOrder(ctx, orderUpdate.AppTransID, orderUpdate.AppID).
					Return(order, nil)

				suite.mockOrderRepo.EXPECT().
					UpdateOrderProgress(ctx, gomock.Any()).
					Return(errors.New("update status error"))

				suite.mockTransaction.EXPECT().
					WithTx(ctx, gomock.Any()).
					DoAndReturn(func(ctx context.Context, fn func(context.Context) error) error {
						return fn(ctx)
					})
			},
			expectedError: "update status error",
		},
	}

	for _, tt := range tests {
		suite.Run(tt.name, func() {
			tt.setupMocks()

			err := suite.usecase.ProcessSettleOrderUpdate(ctx, orderUpdate)

			if tt.expectedError != "" {
				suite.Error(err)
				suite.Contains(err.Error(), tt.expectedError)
			} else {
				suite.NoError(err)
			}
		})
	}
}

// Test DispatchRefundSettleChanged - Complete coverage
func (suite *SettleTestSuite) TestDispatchRefundSettleChanged() {
	ctx := context.Background()
	settleID := int64(123)

	tests := []struct {
		name          string
		setupMocks    func()
		expectedError string
	}{
		{
			name: "success_no_snapshot_change",
			setupMocks: func() {
				settle := &model.RefundSettle{
					ID:        settleID,
					ZPTransID: 456,
					Status:    model.RefundSettleStatusPending,
					ExtraData: model.RefundSettleExtra{
						EventSnapshot: &model.RefundSettleSnapshot{
							Status:            model.RefundSettleStatusPending,
							EventVersion:      0,
							UserTopupAmount:   0,
							NetRefundAmount:   0,
							TotalRefundAmount: 0,
						},
					},
				}

				suite.mockTransaction.EXPECT().
					BeginTx(ctx).
					Return(ctx, nil)

				suite.mockRefundRepo.EXPECT().
					GetRefundSettleByID(ctx, settleID).
					Return(settle, nil)

				suite.mockTransaction.EXPECT().
					CommitTx(ctx).
					Return(nil)

				suite.mockTransaction.EXPECT().
					RollbackTx(ctx).
					Return(nil).
					AnyTimes()
			},
			expectedError: "",
		},
		{
			name: "success_with_snapshot_change",
			setupMocks: func() {
				settle := &model.RefundSettle{
					ID:        settleID,
					ZPTransID: 456,
					Status:    model.RefundSettleStatusInit, // This will trigger snapshot change
					ExtraData: model.RefundSettleExtra{},    // Empty to trigger snapshot change
				}

				suite.mockTransaction.EXPECT().
					BeginTx(ctx).
					Return(ctx, nil)

				suite.mockRefundRepo.EXPECT().
					GetRefundSettleByID(ctx, settleID).
					Return(settle, nil)

				suite.mockRefundRepo.EXPECT().
					UpdateRefundSettleEventData(ctx, settle, gomock.Any()).
					Return(nil)

				suite.mockInstallmentAdapter.EXPECT().
					NotifyInstallmentRefund(ctx, settle).
					Return(nil)

				suite.mockTransaction.EXPECT().
					CommitTx(ctx).
					Return(nil)

				suite.mockTransaction.EXPECT().
					RollbackTx(ctx).
					Return(nil).
					AnyTimes()
			},
			expectedError: "",
		},
		{
			name: "error_begin_tx",
			setupMocks: func() {
				suite.mockTransaction.EXPECT().
					BeginTx(ctx).
					Return(nil, errors.New("begin tx error")).
					Times(3)
			},
			expectedError: "begin tx error",
		},
		{
			name: "error_get_refund_settle",
			setupMocks: func() {
				suite.mockTransaction.EXPECT().
					BeginTx(ctx).
					Return(ctx, nil).
					Times(3)

				suite.mockRefundRepo.EXPECT().
					GetRefundSettleByID(ctx, settleID).
					Return(nil, errors.New("get settle error")).
					Times(3)

				suite.mockTransaction.EXPECT().
					RollbackTx(ctx).
					Return(nil).
					Times(3)
			},
			expectedError: "get settle error",
		},
		{
			name: "error_update_event_data",
			setupMocks: func() {
				settle := &model.RefundSettle{
					ID:        settleID,
					ZPTransID: 456,
					Status:    model.RefundSettleStatusInit, // This will trigger snapshot change
					ExtraData: model.RefundSettleExtra{},    // Empty to trigger snapshot change
				}

				suite.mockTransaction.EXPECT().
					BeginTx(ctx).
					Return(ctx, nil).
					Times(3)

				suite.mockRefundRepo.EXPECT().
					GetRefundSettleByID(ctx, settleID).
					Return(settle, nil).
					Times(3)

				suite.mockRefundRepo.EXPECT().
					UpdateRefundSettleEventData(ctx, settle, gomock.Any()).
					Return(errors.New("update event data error")).
					Times(3)

				suite.mockTransaction.EXPECT().
					RollbackTx(ctx).
					Return(nil).
					Times(3)
			},
			expectedError: "update event data error",
		},
		{
			name: "error_notify_installment",
			setupMocks: func() {
				settle := &model.RefundSettle{
					ID:        settleID,
					ZPTransID: 456,
					Status:    model.RefundSettleStatusInit, // This will trigger snapshot change
					ExtraData: model.RefundSettleExtra{},    // Empty to trigger snapshot change
				}

				suite.mockTransaction.EXPECT().
					BeginTx(ctx).
					Return(ctx, nil).
					Times(3)

				suite.mockRefundRepo.EXPECT().
					GetRefundSettleByID(ctx, settleID).
					Return(settle, nil).
					Times(3)

				suite.mockRefundRepo.EXPECT().
					UpdateRefundSettleEventData(ctx, settle, gomock.Any()).
					Return(nil).
					Times(3)

				suite.mockInstallmentAdapter.EXPECT().
					NotifyInstallmentRefund(ctx, settle).
					Return(errors.New("notify installment error")).
					Times(3)

				suite.mockTransaction.EXPECT().
					RollbackTx(ctx).
					Return(nil).
					Times(3)
			},
			expectedError: "notify installment error",
		},
		{
			name: "error_commit_tx",
			setupMocks: func() {
				settle := &model.RefundSettle{
					ID:        settleID,
					ZPTransID: 456,
					Status:    model.RefundSettleStatusInit, // This will trigger snapshot change
					ExtraData: model.RefundSettleExtra{},    // Empty to trigger snapshot change
				}

				suite.mockTransaction.EXPECT().
					BeginTx(ctx).
					Return(ctx, nil).
					Times(3)

				suite.mockRefundRepo.EXPECT().
					GetRefundSettleByID(ctx, settleID).
					Return(settle, nil).
					Times(3)

				suite.mockRefundRepo.EXPECT().
					UpdateRefundSettleEventData(ctx, settle, gomock.Any()).
					Return(nil).
					Times(3)

				suite.mockInstallmentAdapter.EXPECT().
					NotifyInstallmentRefund(ctx, settle).
					Return(nil).
					Times(3)

				suite.mockTransaction.EXPECT().
					CommitTx(ctx).
					Return(errors.New("commit error")).
					Times(3)

				suite.mockTransaction.EXPECT().
					RollbackTx(ctx).
					Return(nil).
					Times(3)
			},
			expectedError: "commit error",
		},
	}

	for _, tt := range tests {
		suite.Run(tt.name, func() {
			tt.setupMocks()

			err := suite.usecase.DispatchRefundSettleChanged(ctx, settleID)

			if tt.expectedError != "" {
				suite.Error(err)
				suite.Contains(err.Error(), tt.expectedError)
			} else {
				suite.NoError(err)
			}
		})
	}
}

// Test buildRefundSettleOrder - Complete coverage
func (suite *SettleTestSuite) TestBuildRefundSettleOrder() {
	settle := &model.RefundSettle{
		ID:               123,
		ZPTransID:        456,
		SettlementAmount: 1000,
	}
	payment := &model.PurchaseOrder{
		AccountInfo: model.Account{
			ZalopayID:   12345,
			PartnerCode: "PARTNER_A",
		},
	}

	tests := []struct {
		name          string
		setupMocks    func()
		expectedError string
	}{
		{
			name: "success_build_order",
			setupMocks: func() {
				orderConfigs := &configs.OrderConfigs{
					RefundSettle: []*configs.OrderConfig{{AppId: 4143, PartnerCode: nil}},
				}
				suite.orderConfigsHelper = configs.NewOrderConfigs(orderConfigs)
				suite.usecase.orderCfgs = suite.orderConfigsHelper
			},
			expectedError: "",
		},
		{
			name: "error_config_not_found",
			setupMocks: func() {
				emptyOrderConfigs := &configs.OrderConfigs{}
				suite.orderConfigsHelper = configs.NewOrderConfigs(emptyOrderConfigs)
				suite.usecase.orderCfgs = suite.orderConfigsHelper
			},
			expectedError: "refund settle order config not found",
		},
	}

	for _, tt := range tests {
		suite.Run(tt.name, func() {
			tt.setupMocks()

			result, err := suite.usecase.buildRefundSettleOrder(settle, payment)

			if tt.expectedError != "" {
				suite.Error(err)
				suite.Contains(err.Error(), tt.expectedError)
				suite.Nil(result)
			} else {
				suite.NoError(err)
				suite.NotNil(result)
				suite.Equal(settle.ZPTransID, result.ExtraData.RefZPTransID)
				suite.Equal(settle.SettlementAmount, result.Amount)
				suite.Equal(settle.ID, result.RefundSettleID)
			}
		})
	}
}

// Test handleSubmitEarlyDischargeError - Complete coverage
func (suite *SettleTestSuite) TestHandleSubmitEarlyDischargeError() {
	ctx := context.Background()
	submitErr := errors.New("submit error")
	dischargeLog := &model.EarlyDischargeLog{
		ID: 123,
		AccountInfo: model.Account{
			ZalopayID: 12345,
		},
	}

	tests := []struct {
		name          string
		setupMocks    func()
		expectedError string
	}{
		{
			name: "success_handle_error",
			setupMocks: func() {
				suite.mockPaymentRepo.EXPECT().
					UpdateEarlyDischargeLog(ctx, gomock.Any()).
					Return(dischargeLog, nil).
					Times(3) // Retry mechanism

				suite.mockTaskJobAdapter.EXPECT().
					ExecuteEarlyDischargePollingJob(ctx, gomock.Any()).
					Return(nil).
					Times(3) // Retry mechanism
			},
			expectedError: "",
		},
		{
			name: "error_update_discharge_log",
			setupMocks: func() {
				suite.mockPaymentRepo.EXPECT().
					UpdateEarlyDischargeLog(ctx, gomock.Any()).
					Return(nil, errors.New("update error")).
					Times(3) // Retry mechanism
			},
			expectedError: "update error",
		},
		{
			name: "error_trigger_polling",
			setupMocks: func() {
				suite.mockPaymentRepo.EXPECT().
					UpdateEarlyDischargeLog(ctx, gomock.Any()).
					Return(dischargeLog, nil).
					Times(3) // Retry mechanism

				suite.mockTaskJobAdapter.EXPECT().
					ExecuteEarlyDischargePollingJob(ctx, gomock.Any()).
					Return(errors.New("polling error")).
					Times(3) // Retry mechanism
			},
			expectedError: "polling error",
		},
	}

	for _, tt := range tests {
		suite.Run(tt.name, func() {
			tt.setupMocks()

			err := suite.usecase.handleSubmitEarlyDischargeError(ctx, submitErr, dischargeLog)

			if tt.expectedError != "" {
				suite.Error(err)
				suite.Contains(err.Error(), tt.expectedError)
			} else {
				suite.NoError(err)
			}
		})
	}
}

// Test handleSubmitEarlyDischargeResult - Complete coverage
func (suite *SettleTestSuite) TestHandleSubmitEarlyDischargeResult() {
	ctx := context.Background()
	settle := &model.RefundSettle{
		ID:        123,
		ZPTransID: 456,
	}
	dischargeLog := &model.EarlyDischargeLog{
		ID: 789,
		Order: model.Order{
			AppTransID: "test_app_trans_id",
			AppID:      4143,
		},
		AccountInfo: model.Account{
			ZalopayID: 12345,
		},
	}

	tests := []struct {
		name          string
		result        *model.EarlyDischargeResult
		setupMocks    func()
		expectedError string
	}{
		{
			name: "success_succeeded_no_reconciliation",
			result: &model.EarlyDischargeResult{
				TransactionStatus: model.CIMBPaymentStatusComplete,
				NeedReconcilation: false,
			},
			setupMocks: func() {
				suite.mockPaymentRepo.EXPECT().
					UpdateEarlyDischargeLogStatus(ctx, dischargeLog.ID, gomock.Any()).
					Return(nil)

				// PublishRefundSettleResult calls GetRefundSettleOrder
				suite.mockOrderRepo.EXPECT().
					GetRefundSettleOrder(ctx, dischargeLog.Order.AppTransID, dischargeLog.Order.AppID).
					Return(&model.RefundSettleOrder{RefundSettleID: 123}, nil)

				suite.mockRefundRepo.EXPECT().
					GetRefundSettleByID(ctx, int64(123)).
					Return(&model.RefundSettle{ID: 123}, nil)

				suite.mockRefundSettleNotifier.EXPECT().
					PublishRefundSettleResult(ctx, gomock.Any(), dischargeLog).
					Return(nil)
			},
			expectedError: "",
		},
		{
			name: "success_succeeded_with_reconciliation",
			result: &model.EarlyDischargeResult{
				TransactionStatus: model.CIMBPaymentStatusComplete,
				NeedReconcilation: true,
			},
			setupMocks: func() {
				suite.mockPaymentRepo.EXPECT().
					UpdateEarlyDischargeLogStatus(ctx, dischargeLog.ID, gomock.Any()).
					Return(nil)

				// PublishRefundSettleResult calls GetRefundSettleOrder
				suite.mockOrderRepo.EXPECT().
					GetRefundSettleOrder(ctx, dischargeLog.Order.AppTransID, dischargeLog.Order.AppID).
					Return(&model.RefundSettleOrder{RefundSettleID: 123}, nil)

				suite.mockRefundRepo.EXPECT().
					GetRefundSettleByID(ctx, int64(123)).
					Return(&model.RefundSettle{ID: 123}, nil)

				suite.mockRefundSettleNotifier.EXPECT().
					PublishRefundSettleResult(ctx, gomock.Any(), dischargeLog).
					Return(nil)

				suite.mockTaskJobAdapter.EXPECT().
					ExecuteReconcileRefundSettleJob(ctx, gomock.Any()).
					Return(nil).
					Times(3) // Retry mechanism
			},
			expectedError: "",
		},
		{
			name: "success_failed",
			result: &model.EarlyDischargeResult{
				TransactionStatus: model.CIMBPaymentStatusFailed,
				ErrorCode:         "ERROR_001",
				NeedReconcilation: false,
			},
			setupMocks: func() {
				suite.mockPaymentRepo.EXPECT().
					UpdateEarlyDischargeLogStatus(ctx, dischargeLog.ID, gomock.Any()).
					Return(nil)

				// PublishRefundSettleResult calls GetRefundSettleOrder
				suite.mockOrderRepo.EXPECT().
					GetRefundSettleOrder(ctx, dischargeLog.Order.AppTransID, dischargeLog.Order.AppID).
					Return(&model.RefundSettleOrder{RefundSettleID: 123}, nil)

				suite.mockRefundRepo.EXPECT().
					GetRefundSettleByID(ctx, int64(123)).
					Return(&model.RefundSettle{ID: 123}, nil)

				suite.mockRefundSettleNotifier.EXPECT().
					PublishRefundSettleResult(ctx, gomock.Any(), dischargeLog).
					Return(nil)
			},
			expectedError: "",
		},
		{
			name: "success_pending",
			result: &model.EarlyDischargeResult{
				TransactionStatus: model.CIMBPaymentStatusProcessing,
			},
			setupMocks: func() {
				suite.mockPaymentRepo.EXPECT().
					UpdateEarlyDischargeLogStatus(ctx, dischargeLog.ID, gomock.Any()).
					Return(nil)

				suite.mockTaskJobAdapter.EXPECT().
					ExecuteEarlyDischargePollingJob(ctx, gomock.Any()).
					Return(nil)
			},
			expectedError: "",
		},
		{
			name: "error_update_status",
			result: &model.EarlyDischargeResult{
				TransactionStatus: model.CIMBPaymentStatusComplete,
			},
			setupMocks: func() {
				suite.mockPaymentRepo.EXPECT().
					UpdateEarlyDischargeLogStatus(ctx, dischargeLog.ID, gomock.Any()).
					Return(errors.New("update status error"))
			},
			expectedError: "update status error",
		},
		{
			name: "error_publish_result",
			result: &model.EarlyDischargeResult{
				TransactionStatus: model.CIMBPaymentStatusComplete,
			},
			setupMocks: func() {
				suite.mockPaymentRepo.EXPECT().
					UpdateEarlyDischargeLogStatus(ctx, dischargeLog.ID, gomock.Any()).
					Return(nil)

				// PublishRefundSettleResult calls GetRefundSettleOrder
				suite.mockOrderRepo.EXPECT().
					GetRefundSettleOrder(ctx, dischargeLog.Order.AppTransID, dischargeLog.Order.AppID).
					Return(&model.RefundSettleOrder{RefundSettleID: 123}, nil)

				suite.mockRefundRepo.EXPECT().
					GetRefundSettleByID(ctx, int64(123)).
					Return(&model.RefundSettle{ID: 123}, nil)

				suite.mockRefundSettleNotifier.EXPECT().
					PublishRefundSettleResult(ctx, gomock.Any(), dischargeLog).
					Return(errors.New("publish error"))
			},
			expectedError: "publish error",
		},
		{
			name: "error_trigger_reconciliation",
			result: &model.EarlyDischargeResult{
				TransactionStatus: model.CIMBPaymentStatusComplete,
				NeedReconcilation: true,
			},
			setupMocks: func() {
				suite.mockPaymentRepo.EXPECT().
					UpdateEarlyDischargeLogStatus(ctx, dischargeLog.ID, gomock.Any()).
					Return(nil)

				// PublishRefundSettleResult calls GetRefundSettleOrder
				suite.mockOrderRepo.EXPECT().
					GetRefundSettleOrder(ctx, dischargeLog.Order.AppTransID, dischargeLog.Order.AppID).
					Return(&model.RefundSettleOrder{RefundSettleID: 123}, nil)

				suite.mockRefundRepo.EXPECT().
					GetRefundSettleByID(ctx, int64(123)).
					Return(&model.RefundSettle{ID: 123}, nil)

				suite.mockRefundSettleNotifier.EXPECT().
					PublishRefundSettleResult(ctx, gomock.Any(), dischargeLog).
					Return(nil)

				suite.mockTaskJobAdapter.EXPECT().
					ExecuteReconcileRefundSettleJob(ctx, gomock.Any()).
					Return(errors.New("reconciliation error")).
					Times(3) // Retry mechanism
			},
			expectedError: "reconciliation error",
		},
		{
			name: "error_trigger_polling",
			result: &model.EarlyDischargeResult{
				TransactionStatus: model.CIMBPaymentStatusProcessing,
			},
			setupMocks: func() {
				suite.mockPaymentRepo.EXPECT().
					UpdateEarlyDischargeLogStatus(ctx, dischargeLog.ID, gomock.Any()).
					Return(nil)

				suite.mockTaskJobAdapter.EXPECT().
					ExecuteEarlyDischargePollingJob(ctx, gomock.Any()).
					Return(errors.New("polling error"))
			},
			expectedError: "polling error",
		},
		{
			name: "error_invalid_status",
			result: &model.EarlyDischargeResult{
				TransactionStatus: "INVALID_STATUS",
			},
			setupMocks: func() {
				suite.mockPaymentRepo.EXPECT().
					UpdateEarlyDischargeLogStatus(ctx, dischargeLog.ID, gomock.Any()).
					Return(nil)
			},
			expectedError: "invalid discharge log status",
		},
	}

	for _, tt := range tests {
		suite.Run(tt.name, func() {
			tt.setupMocks()

			err := suite.usecase.handleSubmitEarlyDischargeResult(ctx, settle, tt.result, dischargeLog)

			if tt.expectedError != "" {
				suite.Error(err)
				suite.Contains(err.Error(), tt.expectedError)
			} else {
				suite.NoError(err)
			}
		})
	}
}

// Test updatePaymentLogWithOrderInfo - Complete coverage
func (suite *SettleTestSuite) TestUpdatePaymentLogWithOrderInfo() {
	ctx := context.Background()
	orderID := int64(123)

	tests := []struct {
		name          string
		order         *model.RefundSettleOrder
		setupMocks    func()
		expectedError string
	}{
		{
			name: "success_standard_mode",
			order: &model.RefundSettleOrder{
				ID:             orderID,
				Type:           model.OrderTypeRefund,
				RefundSettleID: 456, // This makes it standard mode
			},
			setupMocks: func() {
				eadLog := &model.EarlyDischargeLog{
					ID: 456,
					Order: model.Order{
						ID:         orderID,
						AppTransID: "test_app_trans_id",
						AppID:      789,
					},
				}
				suite.mockPaymentRepo.EXPECT().
					GetEarlyDischargeLogByOrderID(ctx, orderID).
					Return(eadLog, nil)

				suite.mockPaymentRepo.EXPECT().
					UpdatePaymentLogOrderIDs(ctx, eadLog.ID, gomock.Any()).
					Return(nil)
			},
			expectedError: "",
		},
		{
			name: "success_expired_mode",
			order: &model.RefundSettleOrder{
				ID:       orderID,
				Type:     model.OrderTypeRefund,
				RefundID: 123, // This makes it expired mode
			},
			setupMocks: func() {
				repayLog := &model.RepaymentLog{
					ID: 456,
					Order: model.Order{
						ID:         orderID,
						AppTransID: "test_app_trans_id",
						AppID:      789,
					},
				}
				suite.mockPaymentRepo.EXPECT().
					GetRepaymentByOrderID(ctx, orderID).
					Return(repayLog, nil)

				suite.mockPaymentRepo.EXPECT().
					UpdatePaymentLogOrderIDs(ctx, repayLog.ID, gomock.Any()).
					Return(nil)
			},
			expectedError: "",
		},
		{
			name: "error_get_early_discharge_log",
			order: &model.RefundSettleOrder{
				ID:             orderID,
				Type:           model.OrderTypeRefund,
				RefundSettleID: 456, // This makes it standard mode
			},
			setupMocks: func() {
				suite.mockPaymentRepo.EXPECT().
					GetEarlyDischargeLogByOrderID(ctx, orderID).
					Return(nil, errors.New("get early discharge log error"))
			},
			expectedError: "get early discharge log error",
		},
		{
			name: "error_get_repayment_log",
			order: &model.RefundSettleOrder{
				ID:       orderID,
				Type:     model.OrderTypeRefund,
				RefundID: 123, // This makes it expired mode
			},
			setupMocks: func() {
				suite.mockPaymentRepo.EXPECT().
					GetRepaymentByOrderID(ctx, orderID).
					Return(nil, errors.New("get repayment log error"))
			},
			expectedError: "get repayment log error",
		},
		{
			name: "error_unknown_mode",
			order: &model.RefundSettleOrder{
				ID:   orderID,
				Type: model.OrderTypeRefund,
				// Neither RefundID nor RefundSettleID set - this will trigger unknown mode (returns 0)
			},
			setupMocks: func() {
				// No mocks needed for this case
			},
			expectedError: "unknown refund settle mode",
		},
		{
			name: "error_update_payment_log",
			order: &model.RefundSettleOrder{
				ID:             orderID,
				Type:           model.OrderTypeRefund,
				RefundSettleID: 456, // This makes it standard mode
			},
			setupMocks: func() {
				eadLog := &model.EarlyDischargeLog{
					ID: 456,
					Order: model.Order{
						ID:         orderID,
						AppTransID: "test_app_trans_id",
						AppID:      789,
					},
				}
				suite.mockPaymentRepo.EXPECT().
					GetEarlyDischargeLogByOrderID(ctx, orderID).
					Return(eadLog, nil)

				suite.mockPaymentRepo.EXPECT().
					UpdatePaymentLogOrderIDs(ctx, eadLog.ID, gomock.Any()).
					Return(errors.New("update payment log error"))
			},
			expectedError: "update payment log error",
		},
	}

	for _, tt := range tests {
		suite.Run(tt.name, func() {
			tt.setupMocks()

			err := suite.usecase.updatePaymentLogWithOrderInfo(ctx, orderID, tt.order)

			if tt.expectedError != "" {
				suite.Error(err)
				suite.Contains(err.Error(), tt.expectedError)
			} else {
				suite.NoError(err)
			}
		})
	}
}

// Test getResourceForPreparingStandardSettle - Error cases
func (suite *SettleTestSuite) TestGetResourceForPreparingStandardSettle() {
	ctx := context.Background()
	refZPTransID := int64(456)

	tests := []struct {
		name          string
		setupMocks    func()
		expectedError string
	}{
		{
			name: "success_get_resources",
			setupMocks: func() {
				payment := &model.PurchaseOrder{
					ZpTransID: "456",
					AccountInfo: model.Account{
						ZalopayID:   12345,
						PartnerCode: "PARTNER_A",
					},
				}
				suite.mockPaymentRepo.EXPECT().
					GetPaymentByTransID(ctx, refZPTransID).
					Return(payment, nil)

				account := model.Account{
					ZalopayID:   12345,
					PartnerCode: "PARTNER_A",
				}
				suite.mockAccountAdapter.EXPECT().
					GetAccount(ctx, payment.AccountInfo.ZalopayID, payment.AccountInfo.PartnerCode).
					Return(account, nil)

				bankRoute := &model.BankRoute{
					BankAccountNumber: "*********",
					BankAccountName:   "Test Account",
				}
				suite.mockPaymentRepo.EXPECT().
					GetBankRoute(ctx, account.PartnerCode, model.TransTypeRePayment).
					Return(bankRoute, nil)
			},
			expectedError: "",
		},
		{
			name: "error_get_payment",
			setupMocks: func() {
				suite.mockPaymentRepo.EXPECT().
					GetPaymentByTransID(ctx, refZPTransID).
					Return(nil, errors.New("get payment error"))
			},
			expectedError: "get payment error",
		},
		{
			name: "error_get_account",
			setupMocks: func() {
				payment := &model.PurchaseOrder{
					ZpTransID: "456",
					AccountInfo: model.Account{
						ZalopayID:   12345,
						PartnerCode: "PARTNER_A",
					},
				}
				suite.mockPaymentRepo.EXPECT().
					GetPaymentByTransID(ctx, refZPTransID).
					Return(payment, nil)

				suite.mockAccountAdapter.EXPECT().
					GetAccount(ctx, payment.AccountInfo.ZalopayID, payment.AccountInfo.PartnerCode).
					Return(model.Account{}, errors.New("get account error"))
			},
			expectedError: "get account error",
		},
		{
			name: "error_get_bank_route",
			setupMocks: func() {
				payment := &model.PurchaseOrder{
					ZpTransID: "456",
					AccountInfo: model.Account{
						ZalopayID:   12345,
						PartnerCode: "PARTNER_A",
					},
				}
				suite.mockPaymentRepo.EXPECT().
					GetPaymentByTransID(ctx, refZPTransID).
					Return(payment, nil)

				account := model.Account{
					ZalopayID:   12345,
					PartnerCode: "PARTNER_A",
				}
				suite.mockAccountAdapter.EXPECT().
					GetAccount(ctx, payment.AccountInfo.ZalopayID, payment.AccountInfo.PartnerCode).
					Return(account, nil)

				suite.mockPaymentRepo.EXPECT().
					GetBankRoute(ctx, account.PartnerCode, model.TransTypeRePayment).
					Return(nil, errors.New("get bank route error"))
			},
			expectedError: "get bank route error",
		},
	}

	for _, tt := range tests {
		suite.Run(tt.name, func() {
			tt.setupMocks()

			account, purchase, bankRoute, err := suite.usecase.getResourceForPreparingStandardSettle(ctx, refZPTransID)

			if tt.expectedError != "" {
				suite.Error(err)
				suite.Contains(err.Error(), tt.expectedError)
				suite.Nil(account)
				suite.Nil(purchase)
				suite.Nil(bankRoute)
			} else {
				suite.NoError(err)
				suite.NotNil(account)
				suite.NotNil(purchase)
				suite.NotNil(bankRoute)
			}
		})
	}
}

// Test ExecuteRefundSettlement - Additional cases for better coverage
func (suite *SettleTestSuite) TestExecuteRefundSettlement_AdditionalCases() {
	ctx := context.Background()
	settleID := int64(123)

	tests := []struct {
		name          string
		setupMocks    func()
		expectedError string
	}{
		{
			name: "error_get_settle_info",
			setupMocks: func() {
				suite.mockRefundRepo.EXPECT().
					GetRefundSettleByID(ctx, settleID).
					Return(nil, errors.New("get settle error"))
			},
			expectedError: "get settle error",
		},
		{
			name: "cannot_start_settlement",
			setupMocks: func() {
				settle := &model.RefundSettle{
					ID:     settleID,
					Status: model.RefundSettleStatusCompleted,
				}
				suite.mockRefundRepo.EXPECT().
					GetRefundSettleByID(ctx, settleID).
					Return(settle, nil)
			},
			expectedError: "",
		},
		{
			name: "error_sync_settlement_amount",
			setupMocks: func() {
				settle := &model.RefundSettle{
					ID:               settleID,
					ZPTransID:        456,
					Status:           model.RefundSettleStatusPending,
					SettlementAmount: 1000,
				}
				suite.mockRefundRepo.EXPECT().
					GetRefundSettleByID(ctx, settleID).
					Return(settle, nil)

				suite.mockInstallmentAdapter.EXPECT().
					GetEarlyDischarge(ctx, settle.ZPTransID, true).
					Return(model.EarlyDischarge{}, errors.New("sync error"))
			},
			expectedError: "sync error",
		},
		{
			name: "success_full_flow",
			setupMocks: func() {
				settle := &model.RefundSettle{
					ID:               settleID,
					ZPTransID:        456,
					Status:           model.RefundSettleStatusPending,
					SettlementAmount: 1000,
					NetRefundAmount:  1000,
				}

				// Get settle info
				suite.mockRefundRepo.EXPECT().
					GetRefundSettleByID(ctx, settleID).
					Return(settle, nil)

				// Sync settlement amount
				earlyDischarge := model.EarlyDischarge{
					IsSettled:   true,
					IsEligible:  false,
					TotalAmount: 1000,
				}
				suite.mockInstallmentAdapter.EXPECT().
					GetEarlyDischarge(ctx, settle.ZPTransID, true).
					Return(earlyDischarge, nil)

				// Get resources
				payment := &model.PurchaseOrder{
					ZpTransID: "456",
					AccountInfo: model.Account{
						ZalopayID:   12345,
						PartnerCode: "PARTNER_A",
					},
				}
				suite.mockPaymentRepo.EXPECT().
					GetPaymentByTransID(ctx, settle.ZPTransID).
					Return(payment, nil)

				account := model.Account{
					ZalopayID:   12345,
					PartnerCode: "PARTNER_A",
				}
				suite.mockAccountAdapter.EXPECT().
					GetAccount(ctx, payment.AccountInfo.ZalopayID, payment.AccountInfo.PartnerCode).
					Return(account, nil)

				bankRoute := &model.BankRoute{
					BankAccountNumber: "*********",
					BankAccountName:   "Test Account",
				}
				suite.mockPaymentRepo.EXPECT().
					GetBankRoute(ctx, account.PartnerCode, model.TransTypeRePayment).
					Return(bankRoute, nil)

				// Transaction operations
				suite.mockTransaction.EXPECT().
					BeginTx(ctx).
					Return(ctx, nil)

				suite.mockRefundRepo.EXPECT().
					UpdateRefundSettleStatusByID(ctx, settleID, model.RefundSettleStatusProcessing).
					Return(nil)

				// Setup order configs
				orderConfigs := &configs.OrderConfigs{
					RefundSettle: []*configs.OrderConfig{{AppId: 4143, PartnerCode: nil}},
				}
				suite.orderConfigsHelper = configs.NewOrderConfigs(orderConfigs)
				suite.usecase.orderCfgs = suite.orderConfigsHelper

				order := &model.RefundSettleOrder{
					ID:             1,
					Amount:         1000,
					RefundSettleID: settleID,
				}
				suite.mockOrderRepo.EXPECT().
					CreateRefundSettleOrder(ctx, gomock.Any()).
					Return(order, nil)

				dischargeLog := &model.EarlyDischargeLog{
					ID:           100,
					PartnerReqID: "test_req_id",
					Order: model.Order{
						Amount:     1000,
						AppTransID: "test_app_trans_id",
						AppID:      4143,
					},
					BankRoute: model.BankRoute{
						BankAccountNumber: "*********",
						BankAccountName:   "Test Account",
					},
					AccountInfo: model.Account{
						ZalopayID: 12345,
					},
				}
				suite.mockPaymentRepo.EXPECT().
					CreateEarlyDischargeLog(ctx, gomock.Any()).
					Return(dischargeLog, nil)

				suite.mockTransaction.EXPECT().
					CommitTx(ctx).
					Return(nil)

				suite.mockTransaction.EXPECT().
					RollbackTx(ctx).
					Return(nil)

				// Get installment status
				installmentStatus := model.GetInstallmentStatusResponse{
					LoanID: "loan123",
					Status: model.InstallmentStatusOpen,
				}
				suite.mockInstallmentAdapter.EXPECT().
					GetInstallmentStatus(ctx, int64(456)).
					Return(installmentStatus, nil)

				// Submit early discharge
				result := &model.EarlyDischargeResult{
					TransactionStatus: model.CIMBPaymentStatusComplete,
					NeedReconcilation: false,
				}
				suite.mockPartnerConnector.EXPECT().
					SubmitEarlyDischarge(ctx, gomock.Any()).
					Return(result, nil)

				// Handle result
				suite.mockPaymentRepo.EXPECT().
					UpdateEarlyDischargeLogStatus(ctx, dischargeLog.ID, gomock.Any()).
					Return(nil)

				// PublishRefundSettleResult calls GetRefundSettleOrder
				suite.mockOrderRepo.EXPECT().
					GetRefundSettleOrder(ctx, dischargeLog.Order.AppTransID, dischargeLog.Order.AppID).
					Return(&model.RefundSettleOrder{RefundSettleID: 123}, nil)

				suite.mockRefundRepo.EXPECT().
					GetRefundSettleByID(ctx, int64(123)).
					Return(&model.RefundSettle{ID: 123}, nil)

				suite.mockRefundSettleNotifier.EXPECT().
					PublishRefundSettleResult(ctx, gomock.Any(), dischargeLog).
					Return(nil)
			},
			expectedError: "",
		},
		{
			name: "error_get_installment_status",
			setupMocks: func() {
				settle := &model.RefundSettle{
					ID:               settleID,
					ZPTransID:        456,
					Status:           model.RefundSettleStatusPending,
					SettlementAmount: 1000,
					NetRefundAmount:  1000,
				}

				// Get settle info
				suite.mockRefundRepo.EXPECT().
					GetRefundSettleByID(ctx, settleID).
					Return(settle, nil)

				// Sync settlement amount
				earlyDischarge := model.EarlyDischarge{
					IsSettled:   true,
					IsEligible:  false,
					TotalAmount: 1000,
				}
				suite.mockInstallmentAdapter.EXPECT().
					GetEarlyDischarge(ctx, settle.ZPTransID, true).
					Return(earlyDischarge, nil)

				// Get resources
				payment := &model.PurchaseOrder{
					ZpTransID: "456",
					AccountInfo: model.Account{
						ZalopayID:   12345,
						PartnerCode: "PARTNER_A",
					},
				}
				suite.mockPaymentRepo.EXPECT().
					GetPaymentByTransID(ctx, settle.ZPTransID).
					Return(payment, nil)

				account := model.Account{
					ZalopayID:   12345,
					PartnerCode: "PARTNER_A",
				}
				suite.mockAccountAdapter.EXPECT().
					GetAccount(ctx, payment.AccountInfo.ZalopayID, payment.AccountInfo.PartnerCode).
					Return(account, nil)

				bankRoute := &model.BankRoute{
					BankAccountNumber: "*********",
					BankAccountName:   "Test Account",
				}
				suite.mockPaymentRepo.EXPECT().
					GetBankRoute(ctx, account.PartnerCode, model.TransTypeRePayment).
					Return(bankRoute, nil)

				// Transaction operations
				suite.mockTransaction.EXPECT().
					BeginTx(ctx).
					Return(ctx, nil)

				suite.mockRefundRepo.EXPECT().
					UpdateRefundSettleStatusByID(ctx, settleID, model.RefundSettleStatusProcessing).
					Return(nil)

				// Setup order configs
				orderConfigs := &configs.OrderConfigs{
					RefundSettle: []*configs.OrderConfig{{AppId: 4143, PartnerCode: nil}},
				}
				suite.orderConfigsHelper = configs.NewOrderConfigs(orderConfigs)
				suite.usecase.orderCfgs = suite.orderConfigsHelper

				order := &model.RefundSettleOrder{
					ID:             1,
					Amount:         1000,
					RefundSettleID: settleID,
				}
				suite.mockOrderRepo.EXPECT().
					CreateRefundSettleOrder(ctx, gomock.Any()).
					Return(order, nil)

				dischargeLog := &model.EarlyDischargeLog{
					ID:           100,
					PartnerReqID: "test_req_id",
				}
				suite.mockPaymentRepo.EXPECT().
					CreateEarlyDischargeLog(ctx, gomock.Any()).
					Return(dischargeLog, nil)

				suite.mockTransaction.EXPECT().
					CommitTx(ctx).
					Return(nil)

				suite.mockTransaction.EXPECT().
					RollbackTx(ctx).
					Return(nil)

				// Get installment status fails
				suite.mockInstallmentAdapter.EXPECT().
					GetInstallmentStatus(ctx, int64(456)).
					Return(model.GetInstallmentStatusResponse{}, errors.New("get installment status error"))
			},
			expectedError: "get installment status error",
		},
		{
			name: "error_submit_early_discharge",
			setupMocks: func() {
				settle := &model.RefundSettle{
					ID:               settleID,
					ZPTransID:        456,
					Status:           model.RefundSettleStatusPending,
					SettlementAmount: 1000,
					NetRefundAmount:  1000,
				}

				// Get settle info
				suite.mockRefundRepo.EXPECT().
					GetRefundSettleByID(ctx, settleID).
					Return(settle, nil)

				// Sync settlement amount
				earlyDischarge := model.EarlyDischarge{
					IsSettled:   true,
					IsEligible:  false,
					TotalAmount: 1000,
				}
				suite.mockInstallmentAdapter.EXPECT().
					GetEarlyDischarge(ctx, settle.ZPTransID, true).
					Return(earlyDischarge, nil)

				// Get resources
				payment := &model.PurchaseOrder{
					ZpTransID: "456",
					AccountInfo: model.Account{
						ZalopayID:   12345,
						PartnerCode: "PARTNER_A",
					},
				}
				suite.mockPaymentRepo.EXPECT().
					GetPaymentByTransID(ctx, settle.ZPTransID).
					Return(payment, nil)

				account := model.Account{
					ZalopayID:   12345,
					PartnerCode: "PARTNER_A",
				}
				suite.mockAccountAdapter.EXPECT().
					GetAccount(ctx, payment.AccountInfo.ZalopayID, payment.AccountInfo.PartnerCode).
					Return(account, nil)

				bankRoute := &model.BankRoute{
					BankAccountNumber: "*********",
					BankAccountName:   "Test Account",
				}
				suite.mockPaymentRepo.EXPECT().
					GetBankRoute(ctx, account.PartnerCode, model.TransTypeRePayment).
					Return(bankRoute, nil)

				// Transaction operations
				suite.mockTransaction.EXPECT().
					BeginTx(ctx).
					Return(ctx, nil)

				suite.mockRefundRepo.EXPECT().
					UpdateRefundSettleStatusByID(ctx, settleID, model.RefundSettleStatusProcessing).
					Return(nil)

				// Setup order configs
				orderConfigs := &configs.OrderConfigs{
					RefundSettle: []*configs.OrderConfig{{AppId: 4143, PartnerCode: nil}},
				}
				suite.orderConfigsHelper = configs.NewOrderConfigs(orderConfigs)
				suite.usecase.orderCfgs = suite.orderConfigsHelper

				order := &model.RefundSettleOrder{
					ID:             1,
					Amount:         1000,
					RefundSettleID: settleID,
				}
				suite.mockOrderRepo.EXPECT().
					CreateRefundSettleOrder(ctx, gomock.Any()).
					Return(order, nil)

				dischargeLog := &model.EarlyDischargeLog{
					ID:           100,
					PartnerReqID: "test_req_id",
					AccountInfo: model.Account{
						ZalopayID: 12345,
					},
				}
				suite.mockPaymentRepo.EXPECT().
					CreateEarlyDischargeLog(ctx, gomock.Any()).
					Return(dischargeLog, nil)

				suite.mockTransaction.EXPECT().
					CommitTx(ctx).
					Return(nil)

				suite.mockTransaction.EXPECT().
					RollbackTx(ctx).
					Return(nil)

				// Get installment status
				installmentStatus := model.GetInstallmentStatusResponse{
					LoanID: "loan123",
					Status: model.InstallmentStatusOpen,
				}
				suite.mockInstallmentAdapter.EXPECT().
					GetInstallmentStatus(ctx, int64(456)).
					Return(installmentStatus, nil)

				// Submit early discharge fails
				suite.mockPartnerConnector.EXPECT().
					SubmitEarlyDischarge(ctx, gomock.Any()).
					Return(nil, errors.New("submit error"))

				// Handle error
				suite.mockPaymentRepo.EXPECT().
					UpdateEarlyDischargeLog(ctx, gomock.Any()).
					Return(dischargeLog, nil)

				suite.mockTaskJobAdapter.EXPECT().
					ExecuteEarlyDischargePollingJob(ctx, gomock.Any()).
					Return(nil)
			},
			expectedError: "",
		},
	}

	for _, tt := range tests {
		suite.Run(tt.name, func() {
			tt.setupMocks()

			err := suite.usecase.ExecuteRefundSettlement(ctx, settleID)

			if tt.expectedError != "" {
				suite.Error(err)
				suite.Contains(err.Error(), tt.expectedError)
			} else {
				suite.NoError(err)
			}
		})
	}
}

func TestSettleTestSuite(t *testing.T) {
	suite.Run(t, new(SettleTestSuite))
}
