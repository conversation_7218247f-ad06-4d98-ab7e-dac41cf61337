package refund

import (
	"context"
	"errors"

	"github.com/stretchr/testify/assert"
	"gitlab.zalopay.vn/fin/installment/payment-service/configs"
	"gitlab.zalopay.vn/fin/installment/payment-service/payment/internal/model"
	"go.uber.org/mock/gomock"
)

// Setup configuration helper
func (suite *RefundTestSuite) setupConfig() {
	orderConfigs := &configs.OrderConfigs{
		RefundSettle: []*configs.OrderConfig{{AppId: 4143, PartnerCode: nil}},
	}
	suite.orderConfigsHelper = configs.NewOrderConfigs(orderConfigs)
	suite.usecase = NewRefundUsecase(
		suite.logger, suite.mockRefundRepo, suite.mockTransaction, suite.mockTaskJobAdapter,
		suite.mockDistributedLock, suite.paymentConfig, suite.orderConfigsHelper,
		suite.mockOrderRepo, suite.mockPaymentRepo, suite.mockPartnerConnector,
		suite.mockAcquiringCore, suite.mockAccountAdapter, suite.mockRefundSettleNotifier,
		suite.mockInstallmentAdapter,
	)
}

// Mock data helpers
func mockExpiredRequest() *ExpiredRefundSettleRequest {
	return &ExpiredRefundSettleRequest{RefundID: 123, SettleAmount: 100000, RefZPTransID: 456}
}

func mockPurchaseOrder() *model.PurchaseOrder {
	return &model.PurchaseOrder{
		AccountInfo: model.Account{ZalopayID: 789, PartnerCode: "CIMB", PartnerAccountId: "acc123"},
	}
}

func mockRepaymentLog() *model.RepaymentLog {
	return &model.RepaymentLog{
		ID: 1, Order: model.Order{ID: 1, Amount: 100000, AppTransID: "test_app_trans_id", AppID: 4143},
		AccountInfo: model.Account{ZalopayID: 789, PartnerAccountId: "acc123"},
		BankRoute:   model.BankRoute{BankAccountNumber: "corp123"},
	}
}

// Main test for ExcuteExpiredRefundSettlement - Success case
func (suite *RefundTestSuite) TestExcuteExpiredRefundSettlement_Success() {
	suite.setupConfig()
	ctx := context.Background()
	params := mockExpiredRequest()

	// Eligibility check
	suite.mockTransaction.EXPECT().BeginTx(gomock.Any()).Return(ctx, nil)
	suite.mockTransaction.EXPECT().RollbackTx(gomock.Any())
	suite.mockRefundRepo.EXPECT().GetRefundLogForUpdate(gomock.Any(), params.RefundID).
		Return(&model.RefundOrder{ID: params.RefundID, ProcessType: model.RefundProcessTypeRepayment}, nil)
	suite.mockOrderRepo.EXPECT().GetRefundSettleOrderByRefundID(gomock.Any(), params.RefundID).
		Return(nil, model.ErrOrderNotFound)

	// Resource gathering
	suite.mockPaymentRepo.EXPECT().GetPaymentByTransID(ctx, params.RefZPTransID).Return(mockPurchaseOrder(), nil)
	suite.mockAccountAdapter.EXPECT().GetAccount(ctx, int64(789), "CIMB").
		Return(model.Account{ZalopayID: 789, PartnerCode: "CIMB"}, nil)
	suite.mockPaymentRepo.EXPECT().GetBankRoute(ctx, "CIMB", model.TransTypeRePayment).
		Return(&model.BankRoute{BankAccountNumber: "*********"}, nil)

	// Main flow
	suite.mockTransaction.EXPECT().BeginTx(gomock.Any()).Return(ctx, nil)
	suite.mockTransaction.EXPECT().RollbackTx(gomock.Any())
	suite.mockOrderRepo.EXPECT().CreateRefundSettleOrder(gomock.Any(), gomock.Any()).
		Return(&model.RefundSettleOrder{ID: 1, Amount: 100000}, nil)
	suite.mockPaymentRepo.EXPECT().CreateRepaymentLog(gomock.Any(), gomock.Any()).Return(mockRepaymentLog(), nil)
	suite.mockTransaction.EXPECT().CommitTx(gomock.Any()).Return(nil)

	// Process repayment
	suite.mockPartnerConnector.EXPECT().ODRepayment(ctx, gomock.Any()).
		Return(&model.RepaymentResult{TransactionStatus: model.CIMBPaymentStatusComplete}, nil)
	suite.mockPaymentRepo.EXPECT().UpdateRepaymentLogStatus(ctx, gomock.Any()).Return(nil)
	suite.mockOrderRepo.EXPECT().GetRefundSettleOrder(ctx, "test_app_trans_id", int32(4143)).
		Return(&model.RefundSettleOrder{ID: 1, Amount: 100000}, nil)
	suite.mockRefundSettleNotifier.EXPECT().PublishRefundExpiredResult(ctx, gomock.Any(), gomock.Any()).Return(nil)

	err := suite.usecase.ExcuteExpiredRefundSettlement(ctx, params)
	assert.NoError(suite.T(), err)
}

// Test not eligible case
func (suite *RefundTestSuite) TestExcuteExpiredRefundSettlement_NotEligible() {
	ctx := context.Background()
	params := mockExpiredRequest()

	suite.mockTransaction.EXPECT().BeginTx(gomock.Any()).Return(ctx, nil)
	suite.mockTransaction.EXPECT().RollbackTx(gomock.Any())
	suite.mockRefundRepo.EXPECT().GetRefundLogForUpdate(gomock.Any(), params.RefundID).
		Return(&model.RefundOrder{ID: params.RefundID, ProcessType: model.RefundProcessTypeSettlement}, nil)

	err := suite.usecase.ExcuteExpiredRefundSettlement(ctx, params)
	assert.NoError(suite.T(), err) // Not eligible is not an error
}

// Test ProcessRepayment - Success case
func (suite *RefundTestSuite) TestProcessRepayment_Success() {
	ctx := context.Background()
	repayLog := mockRepaymentLog()

	suite.mockPartnerConnector.EXPECT().ODRepayment(gomock.Any(), gomock.Any()).
		Return(&model.RepaymentResult{TransactionStatus: model.CIMBPaymentStatusComplete}, nil)
	suite.mockPaymentRepo.EXPECT().UpdateRepaymentLogStatus(gomock.Any(), gomock.Any()).Return(nil)
	suite.mockOrderRepo.EXPECT().GetRefundSettleOrder(gomock.Any(), "test_app_trans_id", int32(4143)).
		Return(&model.RefundSettleOrder{ID: 1, Amount: 100000}, nil)
	suite.mockRefundSettleNotifier.EXPECT().PublishRefundExpiredResult(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

	err := suite.usecase.ProcessRepayment(ctx, repayLog)
	assert.NoError(suite.T(), err)
}

// Test ProcessRepayment - Pending status
func (suite *RefundTestSuite) TestProcessRepayment_PendingStatus() {
	ctx := context.Background()
	repayLog := mockRepaymentLog()

	suite.mockPartnerConnector.EXPECT().ODRepayment(gomock.Any(), gomock.Any()).
		Return(&model.RepaymentResult{TransactionStatus: model.CIMBPaymentStatusProcessing}, nil)
	suite.mockPaymentRepo.EXPECT().UpdateRepaymentLogStatus(gomock.Any(), gomock.Any()).Return(nil)
	suite.mockTaskJobAdapter.EXPECT().ExecuteExpiredRefundRepaymentPollingJob(gomock.Any(), gomock.Any()).Return(nil)

	err := suite.usecase.ProcessRepayment(ctx, repayLog)
	assert.NoError(suite.T(), err)
}

// Test ProcessExpiredRefund - Success case
func (suite *RefundTestSuite) TestProcessExpiredRefund_Success() {
	ctx := context.Background()
	mockSettleIDs := []int64{1, 2}

	// Mock channel building
	suite.mockRefundRepo.EXPECT().GetRefundSettleIDsHasExpiredItem(gomock.Any(), gomock.Any()).
		Return(mockSettleIDs, nil).Times(1)
	suite.mockRefundRepo.EXPECT().GetRefundSettleIDsHasExpiredItem(gomock.Any(), gomock.Any()).
		Return([]int64{}, nil).Times(1)

	// Mock worker processing
	suite.mockTaskJobAdapter.EXPECT().ExecuteReconcileRefundSettleJob(gomock.Any(), gomock.Any()).
		Return(nil).AnyTimes()

	for _, settleID := range mockSettleIDs {
		suite.mockRefundRepo.EXPECT().GetRefundSettleByID(gomock.Any(), settleID).
			Return(&model.RefundSettle{ID: settleID, ZPTransID: settleID + 100}, nil)

		suite.mockTransaction.EXPECT().BeginTx(gomock.Any()).Return(ctx, nil)
		suite.mockTransaction.EXPECT().RollbackTx(gomock.Any())
		suite.mockRefundRepo.EXPECT().GetRefundLogsExpiredByZPTransIDForUpdate(gomock.Any(), settleID+100).
			Return([]*model.RefundOrder{{ID: settleID, ZPTransID: settleID + 100}}, nil)
		suite.mockRefundRepo.EXPECT().MarkRefundLogsAsExpiredByIDs(gomock.Any(), []int64{settleID}).Return(nil)
		suite.mockRefundSettleNotifier.EXPECT().PublishRefundExpiredEvents(gomock.Any(), settleID+100, gomock.Any()).Return(nil)
		suite.mockTransaction.EXPECT().CommitTx(gomock.Any()).Return(nil)
	}

	err := suite.usecase.ProcessExpiredRefund(ctx)
	assert.NoError(suite.T(), err)
}

// Test ProcessExpiredRefund - Nothing to process
func (suite *RefundTestSuite) TestProcessExpiredRefund_NothingToProcess() {
	ctx := context.Background()

	suite.mockRefundRepo.EXPECT().GetRefundSettleIDsHasExpiredItem(gomock.Any(), gomock.Any()).
		Return([]int64{}, nil)

	err := suite.usecase.ProcessExpiredRefund(ctx)
	assert.NoError(suite.T(), err)
}

// Test Refund function - Success case
func (suite *RefundTestSuite) TestRefund_Success() {
	ctx := context.Background()
	req := model.RefundOrder{ZPTransID: 123, Amount: 100000}

	suite.mockPaymentRepo.EXPECT().GetPaymentByTransID(gomock.Any(), int64(123)).
		Return(&model.PurchaseOrder{Status: model.PaymentStatusSucceeded}, nil)
	suite.mockRefundRepo.EXPECT().CreateRefundLog(gomock.Any(), gomock.Any()).
		Return(&model.RefundOrder{ID: 1, Status: model.RefundStatusSuccess}, nil)

	result, err := suite.usecase.Refund(ctx, req)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), result)
}

// Test Refund function - Payment not succeeded
func (suite *RefundTestSuite) TestRefund_PaymentNotSucceeded() {
	ctx := context.Background()
	req := model.RefundOrder{ZPTransID: 123, Amount: 100000}

	suite.mockPaymentRepo.EXPECT().GetPaymentByTransID(gomock.Any(), int64(123)).
		Return(&model.PurchaseOrder{Status: model.PaymentStatusFailed}, nil)

	result, err := suite.usecase.Refund(ctx, req)
	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), result)
	assert.Contains(suite.T(), err.Error(), "payment status is not success")
}

// Test RefundQuery function - Success case
func (suite *RefundTestSuite) TestRefundQuery_Success() {
	ctx := context.Background()
	refundID := int64(123)

	suite.mockRefundRepo.EXPECT().GetRefundLogByRefundID(gomock.Any(), refundID).
		Return(&model.RefundOrder{ID: 123, Status: model.RefundStatusSuccess}, nil)

	result, err := suite.usecase.RefundQuery(ctx, refundID)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), result)
}

// Test RefundQuery function - Refund not found
func (suite *RefundTestSuite) TestRefundQuery_NotFound() {
	ctx := context.Background()
	refundID := int64(123)

	suite.mockRefundRepo.EXPECT().GetRefundLogByRefundID(gomock.Any(), refundID).
		Return(nil, errors.New("refund not found"))

	result, err := suite.usecase.RefundQuery(ctx, refundID)
	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), result)
	assert.Contains(suite.T(), err.Error(), "refund not found")
}

// Test handleSubmitRepaymentFailed - Success case
func (suite *RefundTestSuite) TestHandleSubmitRepaymentFailed_Success() {
	ctx := context.Background()
	repayLog := mockRepaymentLog()
	submitErr := errors.New("submit repayment failed")

	// Mock UpdateRepaymentLogStatus
	suite.mockPaymentRepo.EXPECT().
		UpdateRepaymentLogStatus(ctx, gomock.Any()).
		Return(nil)

	// Mock TriggerPollingRepayStatus
	suite.mockTaskJobAdapter.EXPECT().
		ExecuteExpiredRefundRepaymentPollingJob(ctx, gomock.Any()).
		Return(nil)

	err := suite.usecase.handleSubmitRepaymentFailed(ctx, repayLog, submitErr)

	assert.NoError(suite.T(), err)
}

// Test handleSubmitRepaymentFailed - UpdateRepaymentLogStatus error
func (suite *RefundTestSuite) TestHandleSubmitRepaymentFailed_UpdateRepaymentLogStatusError() {
	ctx := context.Background()
	repayLog := mockRepaymentLog()
	submitErr := errors.New("submit repayment failed")
	expectedErr := errors.New("update repayment log status error")

	// Mock UpdateRepaymentLogStatus to return error
	suite.mockPaymentRepo.EXPECT().
		UpdateRepaymentLogStatus(ctx, gomock.Any()).
		Return(expectedErr)

	err := suite.usecase.handleSubmitRepaymentFailed(ctx, repayLog, submitErr)

	assert.Error(suite.T(), err)
	assert.Equal(suite.T(), expectedErr, err)
}

// Test handleSubmitRepaymentFailed - TriggerPollingRepayStatus error
func (suite *RefundTestSuite) TestHandleSubmitRepaymentFailed_TriggerPollingRepayStatusError() {
	ctx := context.Background()
	repayLog := mockRepaymentLog()
	submitErr := errors.New("submit repayment failed")
	expectedErr := errors.New("trigger polling repay status error")

	// Mock UpdateRepaymentLogStatus
	suite.mockPaymentRepo.EXPECT().
		UpdateRepaymentLogStatus(ctx, gomock.Any()).
		Return(nil)

	// Mock TriggerPollingRepayStatus to return error (retry mechanism will call 3 times)
	suite.mockTaskJobAdapter.EXPECT().
		ExecuteExpiredRefundRepaymentPollingJob(ctx, gomock.Any()).
		Return(expectedErr).
		Times(3)

	err := suite.usecase.handleSubmitRepaymentFailed(ctx, repayLog, submitErr)

	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "trigger polling repay status error")
}

// Test handleSubmitRepaymentFailed - With nil submit error
func (suite *RefundTestSuite) TestHandleSubmitRepaymentFailed_NilSubmitError() {
	ctx := context.Background()
	repayLog := mockRepaymentLog()
	var submitErr error = nil

	// Mock UpdateRepaymentLogStatus
	suite.mockPaymentRepo.EXPECT().
		UpdateRepaymentLogStatus(ctx, gomock.Any()).
		Return(nil)

	// Mock TriggerPollingRepayStatus
	suite.mockTaskJobAdapter.EXPECT().
		ExecuteExpiredRefundRepaymentPollingJob(ctx, gomock.Any()).
		Return(nil)

	err := suite.usecase.handleSubmitRepaymentFailed(ctx, repayLog, submitErr)

	assert.NoError(suite.T(), err)
}

// Test handleSubmitRepaymentFailed - With CIMB error
func (suite *RefundTestSuite) TestHandleSubmitRepaymentFailed_CIMBError() {
	ctx := context.Background()
	repayLog := mockRepaymentLog()
	submitErr := model.CIMBError{
		ErrorCode:   "CIMB001",
		Description: "CIMB specific error",
	}

	// Mock UpdateRepaymentLogStatus
	suite.mockPaymentRepo.EXPECT().
		UpdateRepaymentLogStatus(ctx, gomock.Any()).
		DoAndReturn(func(ctx context.Context, log model.RepaymentLog) error {
			// Verify that the CIMB error is properly set
			assert.Equal(suite.T(), "CIMB001", log.PartnerData.RepaymentResult.CIMBError.ErrorCode)
			assert.Equal(suite.T(), "CIMB specific error", log.PartnerData.RepaymentResult.CIMBError.Description)
			assert.Equal(suite.T(), model.PaymentStatusPending, log.Status)
			return nil
		})

	// Mock TriggerPollingRepayStatus
	suite.mockTaskJobAdapter.EXPECT().
		ExecuteExpiredRefundRepaymentPollingJob(ctx, gomock.Any()).
		Return(nil)

	err := suite.usecase.handleSubmitRepaymentFailed(ctx, repayLog, submitErr)

	assert.NoError(suite.T(), err)
}

// Test handleSubmitRepaymentFailed - With system error
func (suite *RefundTestSuite) TestHandleSubmitRepaymentFailed_SystemError() {
	ctx := context.Background()
	repayLog := mockRepaymentLog()
	submitErr := errors.New("system connection error")

	// Mock UpdateRepaymentLogStatus
	suite.mockPaymentRepo.EXPECT().
		UpdateRepaymentLogStatus(ctx, gomock.Any()).
		DoAndReturn(func(ctx context.Context, log model.RepaymentLog) error {
			// Verify that the system error is properly set
			assert.Equal(suite.T(), "system_error", log.PartnerData.RepaymentResult.CIMBError.ErrorCode)
			assert.Equal(suite.T(), "system connection error", log.PartnerData.RepaymentResult.CIMBError.Description)
			assert.Equal(suite.T(), model.PaymentStatusPending, log.Status)
			return nil
		})

	// Mock TriggerPollingRepayStatus
	suite.mockTaskJobAdapter.EXPECT().
		ExecuteExpiredRefundRepaymentPollingJob(ctx, gomock.Any()).
		Return(nil)

	err := suite.usecase.handleSubmitRepaymentFailed(ctx, repayLog, submitErr)

	assert.NoError(suite.T(), err)
}

// Test handleSubmitRepaymentFailed - TriggerPollingRepayStatus retry exhausted
func (suite *RefundTestSuite) TestHandleSubmitRepaymentFailed_TriggerPollingRetryExhausted() {
	ctx := context.Background()
	repayLog := mockRepaymentLog()
	submitErr := errors.New("submit repayment failed")
	expectedErr := errors.New("polling job execution failed")

	// Mock UpdateRepaymentLogStatus
	suite.mockPaymentRepo.EXPECT().
		UpdateRepaymentLogStatus(ctx, gomock.Any()).
		Return(nil)

	// Mock TriggerPollingRepayStatus to fail all retry attempts (3 times)
	suite.mockTaskJobAdapter.EXPECT().
		ExecuteExpiredRefundRepaymentPollingJob(ctx, gomock.Any()).
		Return(expectedErr).
		Times(3)

	err := suite.usecase.handleSubmitRepaymentFailed(ctx, repayLog, submitErr)

	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "polling job execution failed")
}

// Test ExcuteExpiredRefundSettlement - Error cases for better coverage
func (suite *RefundTestSuite) TestExcuteExpiredRefundSettlement_EligibilityCheckError() {
	suite.setupConfig()
	ctx := context.Background()
	params := mockExpiredRequest()

	suite.mockTransaction.EXPECT().BeginTx(gomock.Any()).Return(ctx, nil)
	suite.mockTransaction.EXPECT().RollbackTx(gomock.Any())
	suite.mockRefundRepo.EXPECT().GetRefundLogForUpdate(gomock.Any(), params.RefundID).
		Return(nil, errors.New("database error"))

	err := suite.usecase.ExcuteExpiredRefundSettlement(ctx, params)
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "database error")
}

func (suite *RefundTestSuite) TestExcuteExpiredRefundSettlement_GetResourceError() {
	suite.setupConfig()
	ctx := context.Background()
	params := mockExpiredRequest()

	// Eligibility check passes
	suite.mockTransaction.EXPECT().BeginTx(gomock.Any()).Return(ctx, nil)
	suite.mockTransaction.EXPECT().RollbackTx(gomock.Any())
	suite.mockRefundRepo.EXPECT().GetRefundLogForUpdate(gomock.Any(), params.RefundID).
		Return(&model.RefundOrder{ID: params.RefundID, ProcessType: model.RefundProcessTypeRepayment}, nil)
	suite.mockOrderRepo.EXPECT().GetRefundSettleOrderByRefundID(gomock.Any(), params.RefundID).
		Return(nil, model.ErrOrderNotFound)

	// Resource gathering fails
	suite.mockPaymentRepo.EXPECT().GetPaymentByTransID(ctx, params.RefZPTransID).
		Return(nil, errors.New("payment not found"))

	err := suite.usecase.ExcuteExpiredRefundSettlement(ctx, params)
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "payment not found")
}

func (suite *RefundTestSuite) TestExcuteExpiredRefundSettlement_BeginTxError() {
	suite.setupConfig()
	ctx := context.Background()
	params := mockExpiredRequest()

	// Eligibility check passes
	suite.mockTransaction.EXPECT().BeginTx(gomock.Any()).Return(ctx, nil)
	suite.mockTransaction.EXPECT().RollbackTx(gomock.Any())
	suite.mockRefundRepo.EXPECT().GetRefundLogForUpdate(gomock.Any(), params.RefundID).
		Return(&model.RefundOrder{ID: params.RefundID, ProcessType: model.RefundProcessTypeRepayment}, nil)
	suite.mockOrderRepo.EXPECT().GetRefundSettleOrderByRefundID(gomock.Any(), params.RefundID).
		Return(nil, model.ErrOrderNotFound)

	// Resource gathering succeeds
	suite.mockPaymentRepo.EXPECT().GetPaymentByTransID(ctx, params.RefZPTransID).Return(mockPurchaseOrder(), nil)
	suite.mockAccountAdapter.EXPECT().GetAccount(ctx, int64(789), "CIMB").
		Return(model.Account{ZalopayID: 789, PartnerCode: "CIMB"}, nil)
	suite.mockPaymentRepo.EXPECT().GetBankRoute(ctx, "CIMB", model.TransTypeRePayment).
		Return(&model.BankRoute{BankAccountNumber: "*********"}, nil)

	// Main transaction fails
	suite.mockTransaction.EXPECT().BeginTx(gomock.Any()).Return(nil, errors.New("begin tx error"))

	err := suite.usecase.ExcuteExpiredRefundSettlement(ctx, params)
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "begin tx error")
}

func (suite *RefundTestSuite) TestExcuteExpiredRefundSettlement_CreateOrderError() {
	suite.setupConfig()
	ctx := context.Background()
	params := mockExpiredRequest()

	// Eligibility check passes
	suite.mockTransaction.EXPECT().BeginTx(gomock.Any()).Return(ctx, nil)
	suite.mockTransaction.EXPECT().RollbackTx(gomock.Any())
	suite.mockRefundRepo.EXPECT().GetRefundLogForUpdate(gomock.Any(), params.RefundID).
		Return(&model.RefundOrder{ID: params.RefundID, ProcessType: model.RefundProcessTypeRepayment}, nil)
	suite.mockOrderRepo.EXPECT().GetRefundSettleOrderByRefundID(gomock.Any(), params.RefundID).
		Return(nil, model.ErrOrderNotFound)

	// Resource gathering succeeds
	suite.mockPaymentRepo.EXPECT().GetPaymentByTransID(ctx, params.RefZPTransID).Return(mockPurchaseOrder(), nil)
	suite.mockAccountAdapter.EXPECT().GetAccount(ctx, int64(789), "CIMB").
		Return(model.Account{ZalopayID: 789, PartnerCode: "CIMB"}, nil)
	suite.mockPaymentRepo.EXPECT().GetBankRoute(ctx, "CIMB", model.TransTypeRePayment).
		Return(&model.BankRoute{BankAccountNumber: "*********"}, nil)

	// Main transaction starts but create order fails
	suite.mockTransaction.EXPECT().BeginTx(gomock.Any()).Return(ctx, nil)
	suite.mockTransaction.EXPECT().RollbackTx(gomock.Any())
	suite.mockOrderRepo.EXPECT().CreateRefundSettleOrder(gomock.Any(), gomock.Any()).
		Return(nil, errors.New("create order error"))

	err := suite.usecase.ExcuteExpiredRefundSettlement(ctx, params)
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "create order error")
}

func (suite *RefundTestSuite) TestExcuteExpiredRefundSettlement_CreateRepaymentLogError() {
	suite.setupConfig()
	ctx := context.Background()
	params := mockExpiredRequest()

	// Eligibility check passes
	suite.mockTransaction.EXPECT().BeginTx(gomock.Any()).Return(ctx, nil)
	suite.mockTransaction.EXPECT().RollbackTx(gomock.Any())
	suite.mockRefundRepo.EXPECT().GetRefundLogForUpdate(gomock.Any(), params.RefundID).
		Return(&model.RefundOrder{ID: params.RefundID, ProcessType: model.RefundProcessTypeRepayment}, nil)
	suite.mockOrderRepo.EXPECT().GetRefundSettleOrderByRefundID(gomock.Any(), params.RefundID).
		Return(nil, model.ErrOrderNotFound)

	// Resource gathering succeeds
	suite.mockPaymentRepo.EXPECT().GetPaymentByTransID(ctx, params.RefZPTransID).Return(mockPurchaseOrder(), nil)
	suite.mockAccountAdapter.EXPECT().GetAccount(ctx, int64(789), "CIMB").
		Return(model.Account{ZalopayID: 789, PartnerCode: "CIMB"}, nil)
	suite.mockPaymentRepo.EXPECT().GetBankRoute(ctx, "CIMB", model.TransTypeRePayment).
		Return(&model.BankRoute{BankAccountNumber: "*********"}, nil)

	// Main transaction starts, order created but repayment log creation fails
	suite.mockTransaction.EXPECT().BeginTx(gomock.Any()).Return(ctx, nil)
	suite.mockTransaction.EXPECT().RollbackTx(gomock.Any())
	suite.mockOrderRepo.EXPECT().CreateRefundSettleOrder(gomock.Any(), gomock.Any()).
		Return(&model.RefundSettleOrder{ID: 1, Amount: 100000}, nil)
	suite.mockPaymentRepo.EXPECT().CreateRepaymentLog(gomock.Any(), gomock.Any()).
		Return(nil, errors.New("create repayment log error"))

	err := suite.usecase.ExcuteExpiredRefundSettlement(ctx, params)
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "create repayment log error")
}

func (suite *RefundTestSuite) TestExcuteExpiredRefundSettlement_CommitTxError() {
	suite.setupConfig()
	ctx := context.Background()
	params := mockExpiredRequest()

	// Eligibility check passes
	suite.mockTransaction.EXPECT().BeginTx(gomock.Any()).Return(ctx, nil)
	suite.mockTransaction.EXPECT().RollbackTx(gomock.Any())
	suite.mockRefundRepo.EXPECT().GetRefundLogForUpdate(gomock.Any(), params.RefundID).
		Return(&model.RefundOrder{ID: params.RefundID, ProcessType: model.RefundProcessTypeRepayment}, nil)
	suite.mockOrderRepo.EXPECT().GetRefundSettleOrderByRefundID(gomock.Any(), params.RefundID).
		Return(nil, model.ErrOrderNotFound)

	// Resource gathering succeeds
	suite.mockPaymentRepo.EXPECT().GetPaymentByTransID(ctx, params.RefZPTransID).Return(mockPurchaseOrder(), nil)
	suite.mockAccountAdapter.EXPECT().GetAccount(ctx, int64(789), "CIMB").
		Return(model.Account{ZalopayID: 789, PartnerCode: "CIMB"}, nil)
	suite.mockPaymentRepo.EXPECT().GetBankRoute(ctx, "CIMB", model.TransTypeRePayment).
		Return(&model.BankRoute{BankAccountNumber: "*********"}, nil)

	// Main transaction starts, everything succeeds but commit fails
	suite.mockTransaction.EXPECT().BeginTx(gomock.Any()).Return(ctx, nil)
	suite.mockTransaction.EXPECT().RollbackTx(gomock.Any())
	suite.mockOrderRepo.EXPECT().CreateRefundSettleOrder(gomock.Any(), gomock.Any()).
		Return(&model.RefundSettleOrder{ID: 1, Amount: 100000}, nil)
	suite.mockPaymentRepo.EXPECT().CreateRepaymentLog(gomock.Any(), gomock.Any()).Return(mockRepaymentLog(), nil)
	suite.mockTransaction.EXPECT().CommitTx(gomock.Any()).Return(errors.New("commit error"))

	err := suite.usecase.ExcuteExpiredRefundSettlement(ctx, params)
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "commit error")
}

// Test ProcessRepayment - Error cases for better coverage
func (suite *RefundTestSuite) TestProcessRepayment_ODRepaymentError() {
	ctx := context.Background()
	repayLog := mockRepaymentLog()

	suite.mockPartnerConnector.EXPECT().ODRepayment(gomock.Any(), gomock.Any()).
		Return(nil, errors.New("OD repayment error"))
	suite.mockPaymentRepo.EXPECT().UpdateRepaymentLogStatus(ctx, gomock.Any()).Return(nil)
	suite.mockTaskJobAdapter.EXPECT().ExecuteExpiredRefundRepaymentPollingJob(ctx, gomock.Any()).Return(nil)

	err := suite.usecase.ProcessRepayment(ctx, repayLog)
	assert.NoError(suite.T(), err) // Error is handled, not returned
}

func (suite *RefundTestSuite) TestProcessRepayment_ODRepaymentResultError() {
	ctx := context.Background()
	repayLog := mockRepaymentLog()

	suite.mockPartnerConnector.EXPECT().ODRepayment(gomock.Any(), gomock.Any()).
		Return(&model.RepaymentResult{
			TransactionStatus: model.CIMBPaymentStatusFailed,
			CIMBError:         model.CIMBError{ErrorCode: "CIMB_ERROR", Description: "CIMB Error"},
		}, nil)
	suite.mockPaymentRepo.EXPECT().UpdateRepaymentLogStatus(ctx, gomock.Any()).Return(nil)
	suite.mockTaskJobAdapter.EXPECT().ExecuteExpiredRefundRepaymentPollingJob(ctx, gomock.Any()).Return(nil)

	err := suite.usecase.ProcessRepayment(ctx, repayLog)
	assert.NoError(suite.T(), err) // Error is handled, not returned
}

func (suite *RefundTestSuite) TestProcessRepayment_UpdateRepaymentLogError() {
	ctx := context.Background()
	repayLog := mockRepaymentLog()

	suite.mockPartnerConnector.EXPECT().ODRepayment(gomock.Any(), gomock.Any()).
		Return(&model.RepaymentResult{TransactionStatus: model.CIMBPaymentStatusComplete}, nil)
	suite.mockPaymentRepo.EXPECT().UpdateRepaymentLogStatus(gomock.Any(), gomock.Any()).
		Return(errors.New("update repayment log error"))

	err := suite.usecase.ProcessRepayment(ctx, repayLog)
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "update repayment log error")
}

func (suite *RefundTestSuite) TestProcessRepayment_FailedStatus() {
	ctx := context.Background()
	repayLog := mockRepaymentLog()

	suite.mockPartnerConnector.EXPECT().ODRepayment(gomock.Any(), gomock.Any()).
		Return(&model.RepaymentResult{TransactionStatus: model.CIMBPaymentStatusFailed}, nil)
	suite.mockPaymentRepo.EXPECT().UpdateRepaymentLogStatus(gomock.Any(), gomock.Any()).Return(nil)
	suite.mockOrderRepo.EXPECT().GetRefundSettleOrder(gomock.Any(), "test_app_trans_id", int32(4143)).
		Return(&model.RefundSettleOrder{ID: 1, Amount: 100000}, nil)
	suite.mockRefundSettleNotifier.EXPECT().PublishRefundExpiredResult(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

	err := suite.usecase.ProcessRepayment(ctx, repayLog)
	assert.NoError(suite.T(), err)
}

func (suite *RefundTestSuite) TestProcessRepayment_PublishResultError() {
	ctx := context.Background()
	repayLog := mockRepaymentLog()

	suite.mockPartnerConnector.EXPECT().ODRepayment(gomock.Any(), gomock.Any()).
		Return(&model.RepaymentResult{TransactionStatus: model.CIMBPaymentStatusComplete}, nil)
	suite.mockPaymentRepo.EXPECT().UpdateRepaymentLogStatus(gomock.Any(), gomock.Any()).Return(nil)
	suite.mockOrderRepo.EXPECT().GetRefundSettleOrder(gomock.Any(), "test_app_trans_id", int32(4143)).
		Return(&model.RefundSettleOrder{ID: 1, Amount: 100000}, nil).Times(3)
	suite.mockRefundSettleNotifier.EXPECT().PublishRefundExpiredResult(gomock.Any(), gomock.Any(), gomock.Any()).
		Return(errors.New("publish result error")).Times(3)

	err := suite.usecase.ProcessRepayment(ctx, repayLog)
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "publish result error")
}

func (suite *RefundTestSuite) TestProcessRepayment_TriggerPollingError() {
	ctx := context.Background()
	repayLog := mockRepaymentLog()

	suite.mockPartnerConnector.EXPECT().ODRepayment(gomock.Any(), gomock.Any()).
		Return(&model.RepaymentResult{TransactionStatus: model.CIMBPaymentStatusProcessing}, nil)
	suite.mockPaymentRepo.EXPECT().UpdateRepaymentLogStatus(gomock.Any(), gomock.Any()).Return(nil)
	suite.mockTaskJobAdapter.EXPECT().ExecuteExpiredRefundRepaymentPollingJob(gomock.Any(), gomock.Any()).
		Return(errors.New("trigger polling error")).Times(3)

	err := suite.usecase.ProcessRepayment(ctx, repayLog)
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "trigger polling error")
}

func (suite *RefundTestSuite) TestProcessRepayment_InvalidStatus() {
	ctx := context.Background()
	repayLog := mockRepaymentLog()

	suite.mockPartnerConnector.EXPECT().ODRepayment(gomock.Any(), gomock.Any()).
		Return(&model.RepaymentResult{TransactionStatus: "INVALID_STATUS"}, nil)
	suite.mockPaymentRepo.EXPECT().UpdateRepaymentLogStatus(gomock.Any(), gomock.Any()).Return(nil)
	// Invalid status will trigger polling
	suite.mockTaskJobAdapter.EXPECT().ExecuteExpiredRefundRepaymentPollingJob(ctx, gomock.Any()).Return(nil)

	err := suite.usecase.ProcessRepayment(ctx, repayLog)
	assert.NoError(suite.T(), err) // Error is handled, not returned
}

// Test isEligibleForExpiredRefundSettle - More edge cases
func (suite *RefundTestSuite) TestIsEligibleForExpiredRefundSettle_BeginTxError() {
	ctx := context.Background()
	refundID := int64(123)

	suite.mockTransaction.EXPECT().BeginTx(gomock.Any()).Return(nil, errors.New("begin tx error"))

	eligible, err := suite.usecase.isEligibleForExpiredRefundSettle(ctx, refundID)
	assert.Error(suite.T(), err)
	assert.False(suite.T(), eligible)
	assert.Contains(suite.T(), err.Error(), "begin tx error")
}

func (suite *RefundTestSuite) TestIsEligibleForExpiredRefundSettle_GetRefundLogError() {
	ctx := context.Background()
	refundID := int64(123)

	suite.mockTransaction.EXPECT().BeginTx(gomock.Any()).Return(ctx, nil)
	suite.mockTransaction.EXPECT().RollbackTx(gomock.Any())
	suite.mockRefundRepo.EXPECT().GetRefundLogForUpdate(gomock.Any(), refundID).
		Return(nil, errors.New("get refund log error"))

	eligible, err := suite.usecase.isEligibleForExpiredRefundSettle(ctx, refundID)
	assert.Error(suite.T(), err)
	assert.False(suite.T(), eligible)
	assert.Contains(suite.T(), err.Error(), "get refund log error")
}

func (suite *RefundTestSuite) TestIsEligibleForExpiredRefundSettle_NotRepaymentType() {
	ctx := context.Background()
	refundID := int64(123)

	suite.mockTransaction.EXPECT().BeginTx(gomock.Any()).Return(ctx, nil)
	suite.mockTransaction.EXPECT().RollbackTx(gomock.Any())
	suite.mockRefundRepo.EXPECT().GetRefundLogForUpdate(gomock.Any(), refundID).
		Return(&model.RefundOrder{ID: refundID, ProcessType: model.RefundProcessTypeSettlement}, nil)

	eligible, err := suite.usecase.isEligibleForExpiredRefundSettle(ctx, refundID)
	assert.NoError(suite.T(), err)
	assert.False(suite.T(), eligible)
}

func (suite *RefundTestSuite) TestIsEligibleForExpiredRefundSettle_GetSettleOrderError() {
	ctx := context.Background()
	refundID := int64(123)

	suite.mockTransaction.EXPECT().BeginTx(gomock.Any()).Return(ctx, nil)
	suite.mockTransaction.EXPECT().RollbackTx(gomock.Any())
	suite.mockRefundRepo.EXPECT().GetRefundLogForUpdate(gomock.Any(), refundID).
		Return(&model.RefundOrder{ID: refundID, ProcessType: model.RefundProcessTypeRepayment}, nil)
	suite.mockOrderRepo.EXPECT().GetRefundSettleOrderByRefundID(gomock.Any(), refundID).
		Return(nil, errors.New("get settle order error"))

	eligible, err := suite.usecase.isEligibleForExpiredRefundSettle(ctx, refundID)
	assert.Error(suite.T(), err)
	assert.False(suite.T(), eligible)
	assert.Contains(suite.T(), err.Error(), "get settle order error")
}

func (suite *RefundTestSuite) TestIsEligibleForExpiredRefundSettle_GetRepaymentLogError() {
	ctx := context.Background()
	refundID := int64(123)

	suite.mockTransaction.EXPECT().BeginTx(gomock.Any()).Return(ctx, nil)
	suite.mockTransaction.EXPECT().RollbackTx(gomock.Any())
	suite.mockRefundRepo.EXPECT().GetRefundLogForUpdate(gomock.Any(), refundID).
		Return(&model.RefundOrder{ID: refundID, ProcessType: model.RefundProcessTypeRepayment}, nil)
	suite.mockOrderRepo.EXPECT().GetRefundSettleOrderByRefundID(gomock.Any(), refundID).
		Return(&model.RefundSettleOrder{ID: 1}, nil)
	suite.mockPaymentRepo.EXPECT().GetRepaymentByOrderID(gomock.Any(), int64(1)).
		Return(nil, errors.New("get repayment log error"))

	eligible, err := suite.usecase.isEligibleForExpiredRefundSettle(ctx, refundID)
	assert.Error(suite.T(), err)
	assert.False(suite.T(), eligible)
	assert.Contains(suite.T(), err.Error(), "get repayment log error")
}

func (suite *RefundTestSuite) TestIsEligibleForExpiredRefundSettle_CommitTxError() {
	ctx := context.Background()
	refundID := int64(123)

	suite.mockTransaction.EXPECT().BeginTx(gomock.Any()).Return(ctx, nil)
	suite.mockTransaction.EXPECT().RollbackTx(gomock.Any())
	suite.mockRefundRepo.EXPECT().GetRefundLogForUpdate(gomock.Any(), refundID).
		Return(&model.RefundOrder{ID: refundID, ProcessType: model.RefundProcessTypeRepayment}, nil)
	suite.mockOrderRepo.EXPECT().GetRefundSettleOrderByRefundID(gomock.Any(), refundID).
		Return(&model.RefundSettleOrder{ID: 1, Status: model.OrderStatusFailed}, nil)
	suite.mockPaymentRepo.EXPECT().GetRepaymentByOrderID(gomock.Any(), int64(1)).
		Return(&model.RepaymentLog{Status: model.PaymentStatusFailed}, nil)
	suite.mockTransaction.EXPECT().CommitTx(gomock.Any()).Return(errors.New("commit error"))

	eligible, err := suite.usecase.isEligibleForExpiredRefundSettle(ctx, refundID)
	assert.Error(suite.T(), err)
	assert.False(suite.T(), eligible)
	assert.Contains(suite.T(), err.Error(), "commit error")
}

func (suite *RefundTestSuite) TestIsEligibleForExpiredRefundSettle_Success() {
	ctx := context.Background()
	refundID := int64(123)

	suite.mockTransaction.EXPECT().BeginTx(gomock.Any()).Return(ctx, nil)
	suite.mockTransaction.EXPECT().RollbackTx(gomock.Any())
	suite.mockRefundRepo.EXPECT().GetRefundLogForUpdate(gomock.Any(), refundID).
		Return(&model.RefundOrder{ID: refundID, ProcessType: model.RefundProcessTypeRepayment}, nil)
	suite.mockOrderRepo.EXPECT().GetRefundSettleOrderByRefundID(gomock.Any(), refundID).
		Return(&model.RefundSettleOrder{ID: 1, Status: model.OrderStatusFailed}, nil)
	suite.mockPaymentRepo.EXPECT().GetRepaymentByOrderID(gomock.Any(), int64(1)).
		Return(&model.RepaymentLog{Status: model.PaymentStatusFailed}, nil)
	suite.mockTransaction.EXPECT().CommitTx(gomock.Any()).Return(nil)

	eligible, err := suite.usecase.isEligibleForExpiredRefundSettle(ctx, refundID)
	assert.NoError(suite.T(), err)
	assert.True(suite.T(), eligible)
}

// Test getResourceForPreparingExpiredSettle - Error cases
func (suite *RefundTestSuite) TestGetResourceForPreparingExpiredSettle_GetPaymentError() {
	ctx := context.Background()
	refZPTransID := int64(456)

	suite.mockPaymentRepo.EXPECT().GetPaymentByTransID(ctx, refZPTransID).
		Return(nil, errors.New("get payment error"))

	account, purchase, bankRoute, err := suite.usecase.getResourceForPreparingExpiredSettle(ctx, refZPTransID)
	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), account)
	assert.Nil(suite.T(), purchase)
	assert.Nil(suite.T(), bankRoute)
	assert.Contains(suite.T(), err.Error(), "get payment error")
}

func (suite *RefundTestSuite) TestGetResourceForPreparingExpiredSettle_GetAccountError() {
	ctx := context.Background()
	refZPTransID := int64(456)

	suite.mockPaymentRepo.EXPECT().GetPaymentByTransID(ctx, refZPTransID).Return(mockPurchaseOrder(), nil)
	suite.mockAccountAdapter.EXPECT().GetAccount(ctx, int64(789), "CIMB").
		Return(model.Account{}, errors.New("get account error"))

	account, purchase, bankRoute, err := suite.usecase.getResourceForPreparingExpiredSettle(ctx, refZPTransID)
	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), account)
	assert.Nil(suite.T(), purchase)
	assert.Nil(suite.T(), bankRoute)
	assert.Contains(suite.T(), err.Error(), "get account error")
}

func (suite *RefundTestSuite) TestGetResourceForPreparingExpiredSettle_GetBankRouteError() {
	ctx := context.Background()
	refZPTransID := int64(456)

	suite.mockPaymentRepo.EXPECT().GetPaymentByTransID(ctx, refZPTransID).Return(mockPurchaseOrder(), nil)
	suite.mockAccountAdapter.EXPECT().GetAccount(ctx, int64(789), "CIMB").
		Return(model.Account{ZalopayID: 789, PartnerCode: "CIMB"}, nil)
	suite.mockPaymentRepo.EXPECT().GetBankRoute(ctx, "CIMB", model.TransTypeRePayment).
		Return(nil, errors.New("get bank route error"))

	account, purchase, bankRoute, err := suite.usecase.getResourceForPreparingExpiredSettle(ctx, refZPTransID)
	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), account)
	assert.Nil(suite.T(), purchase)
	assert.Nil(suite.T(), bankRoute)
	assert.Contains(suite.T(), err.Error(), "get bank route error")
}

func (suite *RefundTestSuite) TestGetResourceForPreparingExpiredSettle_Success() {
	ctx := context.Background()
	refZPTransID := int64(456)

	suite.mockPaymentRepo.EXPECT().GetPaymentByTransID(ctx, refZPTransID).Return(mockPurchaseOrder(), nil)
	suite.mockAccountAdapter.EXPECT().GetAccount(ctx, int64(789), "CIMB").
		Return(model.Account{ZalopayID: 789, PartnerCode: "CIMB"}, nil)
	suite.mockPaymentRepo.EXPECT().GetBankRoute(ctx, "CIMB", model.TransTypeRePayment).
		Return(&model.BankRoute{BankAccountNumber: "*********"}, nil)

	account, purchase, bankRoute, err := suite.usecase.getResourceForPreparingExpiredSettle(ctx, refZPTransID)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), account)
	assert.NotNil(suite.T(), purchase)
	assert.NotNil(suite.T(), bankRoute)
}

// Test processExpiredRefundLogs - Error cases
func (suite *RefundTestSuite) TestProcessExpiredRefundLogs_BeginTxError() {
	ctx := context.Background()
	settle := &model.RefundSettle{ID: 1, ZPTransID: 456}

	suite.mockTransaction.EXPECT().BeginTx(ctx).Return(nil, errors.New("begin tx error"))

	err := suite.usecase.processExpiredRefundLogs(ctx, settle)
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "begin tx error")
}

func (suite *RefundTestSuite) TestProcessExpiredRefundLogs_GetRefundLogsError() {
	ctx := context.Background()
	settle := &model.RefundSettle{ID: 1, ZPTransID: 456}

	suite.mockTransaction.EXPECT().BeginTx(ctx).Return(ctx, nil)
	suite.mockTransaction.EXPECT().RollbackTx(ctx)
	suite.mockRefundRepo.EXPECT().GetRefundLogsExpiredByZPTransIDForUpdate(ctx, settle.ZPTransID).
		Return(nil, errors.New("get refund logs error"))

	err := suite.usecase.processExpiredRefundLogs(ctx, settle)
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "get refund logs error")
}

func (suite *RefundTestSuite) TestProcessExpiredRefundLogs_NoLogsToProcess() {
	ctx := context.Background()
	settle := &model.RefundSettle{ID: 1, ZPTransID: 456}

	suite.mockTransaction.EXPECT().BeginTx(ctx).Return(ctx, nil)
	suite.mockTransaction.EXPECT().RollbackTx(ctx)
	suite.mockRefundRepo.EXPECT().GetRefundLogsExpiredByZPTransIDForUpdate(ctx, settle.ZPTransID).
		Return([]*model.RefundOrder{}, nil)

	err := suite.usecase.processExpiredRefundLogs(ctx, settle)
	assert.NoError(suite.T(), err)
}

func (suite *RefundTestSuite) TestProcessExpiredRefundLogs_MarkExpiredError() {
	ctx := context.Background()
	settle := &model.RefundSettle{ID: 1, ZPTransID: 456}
	refundLogs := []*model.RefundOrder{{ID: 1, ZPTransID: 456}}

	suite.mockTransaction.EXPECT().BeginTx(ctx).Return(ctx, nil)
	suite.mockTransaction.EXPECT().RollbackTx(ctx)
	suite.mockRefundRepo.EXPECT().GetRefundLogsExpiredByZPTransIDForUpdate(ctx, settle.ZPTransID).
		Return(refundLogs, nil)
	suite.mockRefundRepo.EXPECT().MarkRefundLogsAsExpiredByIDs(ctx, []int64{1}).
		Return(errors.New("mark expired error"))

	err := suite.usecase.processExpiredRefundLogs(ctx, settle)
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "mark expired error")
}

func (suite *RefundTestSuite) TestProcessExpiredRefundLogs_PublishEventsError() {
	ctx := context.Background()
	settle := &model.RefundSettle{ID: 1, ZPTransID: 456}
	refundLogs := []*model.RefundOrder{{ID: 1, ZPTransID: 456}}

	suite.mockTransaction.EXPECT().BeginTx(ctx).Return(ctx, nil)
	suite.mockTransaction.EXPECT().RollbackTx(ctx)
	suite.mockRefundRepo.EXPECT().GetRefundLogsExpiredByZPTransIDForUpdate(ctx, settle.ZPTransID).
		Return(refundLogs, nil)
	suite.mockRefundRepo.EXPECT().MarkRefundLogsAsExpiredByIDs(ctx, []int64{1}).Return(nil)
	suite.mockRefundSettleNotifier.EXPECT().PublishRefundExpiredEvents(ctx, settle.ZPTransID, gomock.Any()).
		Return(errors.New("publish events error"))

	err := suite.usecase.processExpiredRefundLogs(ctx, settle)
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "publish events error")
}

func (suite *RefundTestSuite) TestProcessExpiredRefundLogs_CommitTxError() {
	ctx := context.Background()
	settle := &model.RefundSettle{ID: 1, ZPTransID: 456}
	refundLogs := []*model.RefundOrder{{ID: 1, ZPTransID: 456}}

	suite.mockTransaction.EXPECT().BeginTx(ctx).Return(ctx, nil)
	suite.mockTransaction.EXPECT().RollbackTx(ctx)
	suite.mockRefundRepo.EXPECT().GetRefundLogsExpiredByZPTransIDForUpdate(ctx, settle.ZPTransID).
		Return(refundLogs, nil)
	suite.mockRefundRepo.EXPECT().MarkRefundLogsAsExpiredByIDs(ctx, []int64{1}).Return(nil)
	suite.mockRefundSettleNotifier.EXPECT().PublishRefundExpiredEvents(ctx, settle.ZPTransID, gomock.Any()).Return(nil)
	suite.mockTransaction.EXPECT().CommitTx(ctx).Return(errors.New("commit error"))

	err := suite.usecase.processExpiredRefundLogs(ctx, settle)
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "commit error")
}

// Test buildListRefundLogExpiredCh - Error case
func (suite *RefundTestSuite) TestBuildListRefundLogExpiredCh_GetRefundSettleIDsError() {
	ctx := context.Background()

	suite.mockRefundRepo.EXPECT().GetRefundSettleIDsHasExpiredItem(gomock.Any(), gomock.Any()).
		Return(nil, errors.New("get refund settle ids error"))

	ch, err := suite.usecase.buildListRefundLogExpiredCh(ctx)
	assert.NoError(suite.T(), err) // Error is handled in goroutine
	assert.NotNil(suite.T(), ch)

	// Read from channel to ensure it's closed due to error
	var results []int64
	for id := range ch {
		results = append(results, id)
	}
	assert.Empty(suite.T(), results)
}

// Test PublishRefundExpiredResult - Error cases
func (suite *RefundTestSuite) TestPublishRefundExpiredResult_GetOrderError() {
	ctx := context.Background()
	repayLog := mockRepaymentLog()

	suite.mockOrderRepo.EXPECT().GetRefundSettleOrder(ctx, "test_app_trans_id", int32(4143)).
		Return(nil, errors.New("get order error")).Times(3)

	err := suite.usecase.PublishRefundExpiredResult(ctx, repayLog)
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "get order error")
}

func (suite *RefundTestSuite) TestPublishRefundExpiredResult_PublishError() {
	ctx := context.Background()
	repayLog := mockRepaymentLog()

	suite.mockOrderRepo.EXPECT().GetRefundSettleOrder(ctx, "test_app_trans_id", int32(4143)).
		Return(&model.RefundSettleOrder{ID: 1, Amount: 100000}, nil).Times(3)
	suite.mockRefundSettleNotifier.EXPECT().PublishRefundExpiredResult(ctx, gomock.Any(), gomock.Any()).
		Return(errors.New("publish error")).Times(3)

	err := suite.usecase.PublishRefundExpiredResult(ctx, repayLog)
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "publish error")
}

// Test buildExpiredRefundSettleOrder - Error case
func (suite *RefundTestSuite) TestBuildExpiredRefundSettleOrder_ConfigNotFound() {
	params := mockExpiredRequest()
	payment := mockPurchaseOrder()

	// Use empty config helper that won't have the config
	emptyOrderConfigs := &configs.OrderConfigs{}
	emptyOrderConfigsHelper := configs.NewOrderConfigs(emptyOrderConfigs)
	suite.usecase.orderCfgs = emptyOrderConfigsHelper

	order, err := suite.usecase.buildExpiredRefundSettleOrder(params, payment)
	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), order)
	assert.Contains(suite.T(), err.Error(), "refund settle order config not found")
}

// Test ProcessExpiredRefund - Error in buildListRefundLogExpiredCh
func (suite *RefundTestSuite) TestProcessExpiredRefund_BuildChannelError() {
	ctx := context.Background()

	// Return error immediately to simulate channel build failure
	suite.mockRefundRepo.EXPECT().GetRefundSettleIDsHasExpiredItem(gomock.Any(), gomock.Any()).
		Return(nil, errors.New("channel build error"))

	err := suite.usecase.ProcessExpiredRefund(ctx)
	// The error is handled in the goroutine, so no error is returned
	assert.NoError(suite.T(), err)
}

// Test workerProcessExpired - Error cases
func (suite *RefundTestSuite) TestWorkerProcessExpired_GetRefundSettleError() {
	ctx := context.Background()
	settleIDsCh := make(chan int64, 1)
	settleIDsCh <- 1
	close(settleIDsCh)

	suite.mockRefundRepo.EXPECT().GetRefundSettleByID(gomock.Any(), int64(1)).
		Return(nil, errors.New("get refund settle error"))

	// This should not return error as it's handled in worker
	suite.usecase.workerProcessExpired(ctx, settleIDsCh)
}

func (suite *RefundTestSuite) TestWorkerProcessExpired_ProcessExpiredRefundLogsError() {
	ctx := context.Background()
	settleIDsCh := make(chan int64, 1)
	settleIDsCh <- 1
	close(settleIDsCh)

	suite.mockRefundRepo.EXPECT().GetRefundSettleByID(gomock.Any(), int64(1)).
		Return(&model.RefundSettle{ID: 1, ZPTransID: 101}, nil)

	// Mock processExpiredRefundLogs to fail
	suite.mockTransaction.EXPECT().BeginTx(gomock.Any()).Return(nil, errors.New("process error"))

	// This should not return error as it's handled in worker
	suite.usecase.workerProcessExpired(ctx, settleIDsCh)
}

// Test TriggerPollingRepayStatus - Error case
func (suite *RefundTestSuite) TestTriggerPollingRepayStatus_ExecuteJobError() {
	ctx := context.Background()
	repayLog := mockRepaymentLog()

	suite.mockTaskJobAdapter.EXPECT().ExecuteExpiredRefundRepaymentPollingJob(ctx, gomock.Any()).
		Return(errors.New("execute job error")).Times(3)

	err := suite.usecase.TriggerPollingRepayStatus(ctx, repayLog)
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "execute job error")
}
