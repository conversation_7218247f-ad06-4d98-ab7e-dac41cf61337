package kafkara

import (
	"context"
	"encoding/base64"
	"testing"

	"github.com/segmentio/kafka-go"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.opentelemetry.io/otel/trace"
	kafka_client "zalopay.io/zgo/kafka-client"
)

func TestExtractB3FromHeaders(t *testing.T) {
	tests := []struct {
		name     string
		headers  []kafka.Header
		hasTrace bool
	}{
		{
			name: "Single B3 header format",
			headers: []kafka.Header{
				{Key: "b3", Value: []byte(base64.StdEncoding.EncodeToString([]byte("80f198ee56343ba864fe8b2a57d3eff7-e457b5a2e4d86bd1-1")))},
			},
			hasTrace: true,
		},
		{
			name: "Multi B3 header format",
			headers: []kafka.Header{
				{Key: "x-b3-traceid", Value: []byte(base64.StdEncoding.EncodeToString([]byte("80f198ee56343ba864fe8b2a57d3eff7")))},
				{Key: "x-b3-spanid", Value: []byte(base64.StdEncoding.EncodeToString([]byte("e457b5a2e4d86bd1")))},
				{Key: "x-b3-sampled", Value: []byte(base64.StdEncoding.EncodeToString([]byte("1")))},
			},
			hasTrace: true,
		},
		{
			name: "B3 headers with parent span",
			headers: []kafka.Header{
				{Key: "x-b3-traceid", Value: []byte(base64.StdEncoding.EncodeToString([]byte("80f198ee56343ba864fe8b2a57d3eff7")))},
				{Key: "x-b3-spanid", Value: []byte(base64.StdEncoding.EncodeToString([]byte("e457b5a2e4d86bd1")))},
				{Key: "x-b3-parentspanid", Value: []byte(base64.StdEncoding.EncodeToString([]byte("05e3ac9a4f6e3b90")))},
				{Key: "x-b3-flags", Value: []byte(base64.StdEncoding.EncodeToString([]byte("1")))},
			},
			hasTrace: true,
		},
		{
			name: "Invalid base64 encoding - fallback to original value",
			headers: []kafka.Header{
				{Key: "x-b3-traceid", Value: []byte("invalid-base64!")},
				{Key: "x-b3-spanid", Value: []byte("also-invalid!")},
			},
			hasTrace: true,
		},
		{
			name: "No B3 headers",
			headers: []kafka.Header{
				{Key: "other-header", Value: []byte("some-value")},
				{Key: "content-type", Value: []byte("application/json")},
			},
			hasTrace: false,
		},
		{
			name:     "Empty headers",
			headers:  []kafka.Header{},
			hasTrace: false,
		},
		{
			name: "Mixed headers with some B3",
			headers: []kafka.Header{
				{Key: "content-type", Value: []byte("application/json")},
				{Key: "x-b3-traceid", Value: []byte(base64.StdEncoding.EncodeToString([]byte("80f198ee56343ba864fe8b2a57d3eff7")))},
				{Key: "other-header", Value: []byte("value")},
			},
			hasTrace: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := ExtractB3FromHeaders(tt.headers)

			assert.NotNil(t, ctx)

			// Check if context has span context
			spanCtx := trace.SpanContextFromContext(ctx)
			if tt.hasTrace {
				// Should have some trace context (either extracted or newly created)
				assert.NotNil(t, spanCtx)
			} else {
				// When no B3 headers, a new trace is created, so spanCtx should still be valid
				assert.NotNil(t, spanCtx)
			}
		})
	}
}

func TestInjectB3ToKafkaHeaders(t *testing.T) {
	tests := []struct {
		name          string
		ctx           context.Context
		expectHeaders bool
		expectedKeys  []string
	}{
		{
			name:          "Context with trace",
			ctx:           createContextWithTrace(t),
			expectHeaders: true,
			expectedKeys:  []string{"x-b3-traceid", "x-b3-spanid"},
		},
		{
			name:          "Context without trace",
			ctx:           context.Background(),
			expectHeaders: true,                     // OpenTelemetry creates default headers even for background context
			expectedKeys:  []string{"x-b3-sampled"}, // At minimum, sampled header is created
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			headers := InjectB3ToKafkaHeaders(tt.ctx)

			if tt.expectHeaders {
				assert.NotEmpty(t, headers)

				// Check that headers are base64 encoded
				for _, header := range headers {
					_, err := base64.StdEncoding.DecodeString(string(header.Value))
					assert.NoError(t, err, "Header value should be valid base64")
				}

				// Check for expected keys
				headerKeys := make(map[string]bool)
				for _, header := range headers {
					headerKeys[header.Key] = true
				}

				for _, expectedKey := range tt.expectedKeys {
					assert.True(t, headerKeys[expectedKey], "Expected header key %s not found", expectedKey)
				}
			} else {
				assert.Empty(t, headers)
			}
		})
	}
}

func TestAppendB3HeadersToKafkaMessage(t *testing.T) {
	tests := []struct {
		name             string
		ctx              context.Context
		originalMessage  kafka.Message
		expectNewHeaders bool
	}{
		{
			name: "Append to message with existing headers",
			ctx:  createContextWithTrace(t),
			originalMessage: kafka.Message{
				Key:   []byte("test-key"),
				Value: []byte("test-value"),
				Headers: []kafka.Header{
					{Key: "existing-header", Value: []byte("existing-value")},
				},
			},
			expectNewHeaders: true,
		},
		{
			name: "Append to message without headers",
			ctx:  createContextWithTrace(t),
			originalMessage: kafka.Message{
				Key:     []byte("test-key"),
				Value:   []byte("test-value"),
				Headers: []kafka.Header{},
			},
			expectNewHeaders: true,
		},
		{
			name: "Context without trace",
			ctx:  context.Background(),
			originalMessage: kafka.Message{
				Key:   []byte("test-key"),
				Value: []byte("test-value"),
				Headers: []kafka.Header{
					{Key: "existing-header", Value: []byte("existing-value")},
				},
			},
			expectNewHeaders: true, // OpenTelemetry still adds headers even for background context
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			originalHeaderCount := len(tt.originalMessage.Headers)

			result := AppendB3HeadersToKafkaMessage(tt.ctx, tt.originalMessage)

			// Original message should not be modified
			assert.Equal(t, originalHeaderCount, len(tt.originalMessage.Headers))

			// Result should have same key and value
			assert.Equal(t, tt.originalMessage.Key, result.Key)
			assert.Equal(t, tt.originalMessage.Value, result.Value)

			if tt.expectNewHeaders {
				assert.Greater(t, len(result.Headers), originalHeaderCount)
			} else {
				assert.Equal(t, originalHeaderCount, len(result.Headers))
			}
		})
	}
}

func TestConvertSegmentioHeadersToClientHeaders(t *testing.T) {
	tests := []struct {
		name    string
		headers []kafka.Header
	}{
		{
			name: "Convert multiple headers",
			headers: []kafka.Header{
				{Key: "header1", Value: []byte("value1")},
				{Key: "header2", Value: []byte("value2")},
				{Key: "x-b3-traceid", Value: []byte("trace-id")},
			},
		},
		{
			name:    "Convert empty headers",
			headers: []kafka.Header{},
		},
		{
			name: "Convert single header",
			headers: []kafka.Header{
				{Key: "single", Value: []byte("value")},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := ConvertSegmentioHeadersToClientHeaders(tt.headers)

			assert.Equal(t, len(tt.headers), len(result))

			for i, header := range tt.headers {
				assert.Equal(t, header.Key, result[i].Key)
				assert.Equal(t, header.Value, result[i].Value)
			}
		})
	}
}

func TestInjectB3ToClientKafkaHeaders(t *testing.T) {
	tests := []struct {
		name          string
		ctx           context.Context
		expectHeaders bool
	}{
		{
			name:          "Context with trace",
			ctx:           createContextWithTrace(t),
			expectHeaders: true,
		},
		{
			name:          "Context without trace",
			ctx:           context.Background(),
			expectHeaders: true, // OpenTelemetry creates default headers even for background context
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			headers := InjectB3ToClientKafkaHeaders(tt.ctx)

			// Verify the type is correct
			assert.IsType(t, []kafka_client.Header{}, headers)

			if tt.expectHeaders {
				assert.NotEmpty(t, headers)

				// Verify headers are properly converted
				for _, header := range headers {
					assert.NotEmpty(t, header.Key)
					assert.NotEmpty(t, header.Value)
				}
			} else {
				assert.Empty(t, headers)
			}
		})
	}
}

// Helper function to create a context with trace for testing
func createContextWithTrace(t *testing.T) context.Context {
	// Create a context with existing B3 headers to simulate a traced context
	headers := []kafka.Header{
		{Key: "x-b3-traceid", Value: []byte(base64.StdEncoding.EncodeToString([]byte("80f198ee56343ba864fe8b2a57d3eff7")))},
		{Key: "x-b3-spanid", Value: []byte(base64.StdEncoding.EncodeToString([]byte("e457b5a2e4d86bd1")))},
		{Key: "x-b3-sampled", Value: []byte(base64.StdEncoding.EncodeToString([]byte("1")))},
	}

	// Extract the trace context from headers
	ctx := ExtractB3FromHeaders(headers)

	// Verify we have a valid span context
	spanCtx := trace.SpanContextFromContext(ctx)
	require.True(t, spanCtx.IsValid(), "Should have valid span context")

	return ctx
}
